from django import template

register = template.Library()

@register.filter
def multiply(value, arg):
    """Multiply the value by the argument."""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def divide(value, arg):
    """Divide the value by the argument."""
    try:
        return float(value) / float(arg)
    except (<PERSON><PERSON>rro<PERSON>, TypeError, ZeroDivisionError):
        return 0

@register.filter
def percentage(value, total):
    """Calculate percentage of value from total."""
    try:
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def subtract(value, arg):
    """Subtract arg from value."""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def add_days(date_value, days):
    """Add days to a date."""
    try:
        from datetime import timedelta
        return date_value + timedelta(days=int(days))
    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON>Error, AttributeError):
        return date_value

@register.filter
def random_tile_size(value, index):
    """Generate a random tile size based on priority and index"""
    import random
    priority = int(value) if value else 5

    # Seed random with priority and index for consistency
    random.seed(priority + index)

    # Higher priority dreams have higher chance of being large
    if priority >= 9:
        sizes = ['tile-size-extra-large', 'tile-size-large', 'tile-size-large']
    elif priority >= 7:
        sizes = ['tile-size-large', 'tile-size-medium', 'tile-size-large']
    elif priority >= 5:
        sizes = ['tile-size-medium', 'tile-size-small', 'tile-size-medium']
    else:
        sizes = ['tile-size-small', 'tile-size-small', 'tile-size-medium']

    return random.choice(sizes)

@register.filter
def priority_stars(priority):
    """Generate star display based on priority (1-10 scale)"""
    priority = int(priority) if priority else 0

    if priority >= 9:
        return 5  # 5 stars for priority 9-10
    elif priority >= 7:
        return 4  # 4 stars for priority 7-8
    elif priority >= 5:
        return 3  # 3 stars for priority 5-6
    elif priority >= 3:
        return 2  # 2 stars for priority 3-4
    else:
        return 1  # 1 star for priority 1-2
