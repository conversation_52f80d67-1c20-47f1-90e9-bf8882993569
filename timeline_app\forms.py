from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import UserProfile, Project, ProjectCategory, ProjectTag, DreamItem


class CustomUserCreationForm(UserCreationForm):
    class Meta:
        model = User
        fields = ('username', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter username'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirm password'
        })


class UserProfileForm(forms.ModelForm):
    first_name = forms.CharField(max_length=30, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    last_name = forms.CharField(max_length=30, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    email = forms.EmailField(required=False, widget=forms.EmailInput(attrs={'class': 'form-control'}))

    class Meta:
        model = UserProfile
        fields = [
            'birth_date', 'timezone', 'theme', 'life_expectancy',
            'bio', 'location', 'website', 'email_notifications', 'weekly_reminders'
        ]
        widgets = {
            'birth_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control',
                'placeholder': 'Select your birth date'
            }),
            'timezone': forms.Select(attrs={'class': 'form-control'}),
            'theme': forms.Select(attrs={'class': 'form-control'}),
            'life_expectancy': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '50',
                'max': '120',
                'step': '1'
            }),
            'bio': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Tell us about yourself...'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., New York, USA'
            }),
            'website': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://yourwebsite.com'
            }),
            'email_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'weekly_reminders': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'birth_date': 'Birth Date',
            'timezone': 'Timezone',
            'theme': 'Theme Preference',
            'life_expectancy': 'Life Expectancy (years)',
            'bio': 'Biography',
            'location': 'Location',
            'website': 'Website/Social Media',
            'email_notifications': 'Email Notifications',
            'weekly_reminders': 'Weekly Progress Reminders',
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if user:
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['email'].initial = user.email

    def save(self, commit=True):
        profile = super().save(commit=False)

        if commit:
            # Update user fields
            user = profile.user
            user.first_name = self.cleaned_data['first_name']
            user.last_name = self.cleaned_data['last_name']
            user.email = self.cleaned_data['email']
            user.save()

            profile.save()

        return profile


class ProjectForm(forms.ModelForm):
    tags = forms.ModelMultipleChoiceField(
        queryset=ProjectTag.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
        required=False,
        help_text='Select relevant tags for this project'
    )

    class Meta:
        model = Project
        fields = ['title', 'description', 'category', 'tags', 'deadline', 'priority', 'status', 'progress_percentage', 'notes']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter project title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Describe your project...'
            }),
            'category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'deadline': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-control'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'progress_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '100',
                'step': '5'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Additional notes and updates...'
            }),
        }
        help_texts = {
            'deadline': 'Leave empty for lifetime project (will extend to your 100th birthday)',
            'progress_percentage': 'Set manual progress (0-100%). Leave 0 for automatic calculation.',
        }


class DreamItemForm(forms.ModelForm):
    def clean_image(self):
        image = self.cleaned_data.get('image')
        if image:
            # Check file size (max 5MB)
            if image.size > 5 * 1024 * 1024:
                raise forms.ValidationError("Image file too large. Maximum size is 5MB.")

            # Check file type
            valid_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(image, 'content_type') and image.content_type not in valid_types:
                raise forms.ValidationError("Invalid image format. Please use JPEG, PNG, GIF, or WebP.")

        return image

    class Meta:
        model = DreamItem
        fields = ['title', 'description', 'image', 'image_url', 'category', 'target_date', 'priority', 'notes']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'What do you dream of achieving?'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Describe your dream in detail...'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'image_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://example.com/image.jpg (alternative to file upload)'
            }),
            'category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'target_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'priority': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '10',
                'step': '1'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Additional notes, steps, or thoughts...'
            }),
        }
        help_texts = {
            'image': 'Upload an image of your dream (car, house, destination, etc.)',
            'image_url': 'Or provide an image URL instead of uploading',
            'target_date': 'When do you want to achieve this dream? (optional)',
            'priority': 'How important is this dream to you? (1-10 scale)',
        }
