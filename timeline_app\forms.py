from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import UserProfile, Project, ProjectCategory, ProjectTag, DreamItem, Milestone, DreamMilestone


class CustomUserCreationForm(UserCreationForm):
    class Meta:
        model = User
        fields = ('username', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter username'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirm password'
        })


class UserProfileForm(forms.ModelForm):
    first_name = forms.CharField(max_length=30, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    last_name = forms.CharField(max_length=30, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    email = forms.EmailField(required=False, widget=forms.EmailInput(attrs={'class': 'form-control'}))

    class Meta:
        model = UserProfile
        fields = [
            'birth_date', 'timezone', 'theme', 'life_expectancy',
            'bio', 'location', 'website', 'email_notifications', 'weekly_reminders'
        ]
        widgets = {
            'birth_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control',
                'placeholder': 'Select your birth date'
            }),
            'timezone': forms.Select(attrs={'class': 'form-control'}),
            'theme': forms.Select(attrs={'class': 'form-control'}),
            'life_expectancy': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '50',
                'max': '120',
                'step': '1'
            }),
            'bio': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Tell us about yourself...'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., New York, USA'
            }),
            'website': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://yourwebsite.com'
            }),
            'email_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'weekly_reminders': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'birth_date': 'Birth Date',
            'timezone': 'Timezone',
            'theme': 'Theme Preference',
            'life_expectancy': 'Life Expectancy (years)',
            'bio': 'Biography',
            'location': 'Location',
            'website': 'Website/Social Media',
            'email_notifications': 'Email Notifications',
            'weekly_reminders': 'Weekly Progress Reminders',
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if user:
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['email'].initial = user.email

    def save(self, commit=True):
        profile = super().save(commit=False)

        if commit:
            # Update user fields
            user = profile.user
            user.first_name = self.cleaned_data['first_name']
            user.last_name = self.cleaned_data['last_name']
            user.email = self.cleaned_data['email']
            user.save()

            profile.save()

        return profile


class ProjectForm(forms.ModelForm):
    tags = forms.ModelMultipleChoiceField(
        queryset=ProjectTag.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
        required=False,
        help_text='Select relevant tags for this project'
    )

    class Meta:
        model = Project
        fields = ['title', 'description', 'category', 'tags', 'deadline', 'priority', 'status', 'progress_percentage', 'notes']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter project title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Describe your project...'
            }),
            'category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'deadline': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-control'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'progress_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '100',
                'step': '5'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Additional notes and updates...'
            }),
        }
        help_texts = {
            'deadline': 'Leave empty for lifetime project (will extend to your 100th birthday)',
            'progress_percentage': 'Set manual progress (0-100%). Leave 0 for automatic calculation.',
        }


class DreamItemForm(forms.ModelForm):
    def clean_image(self):
        image = self.cleaned_data.get('image')
        if image:
            try:
                # Check file size (max 10MB initially, we'll resize if needed)
                if image.size > 10 * 1024 * 1024:
                    raise forms.ValidationError(
                        f"Image file too large ({image.size // (1024*1024)}MB). "
                        f"Maximum size is 10MB. Please choose a smaller image."
                    )

                # Check file type
                valid_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
                if hasattr(image, 'content_type'):
                    if image.content_type not in valid_types:
                        raise forms.ValidationError(
                            f"Invalid image format: {image.content_type}. "
                            f"Please use JPEG, PNG, GIF, or WebP format."
                        )

                # Try to validate the image by opening it
                from PIL import Image as PILImage
                try:
                    # Reset file pointer
                    image.seek(0)
                    pil_image = PILImage.open(image)
                    pil_image.verify()  # Verify it's a valid image

                    # Reset file pointer again after verify
                    image.seek(0)

                    # Check image dimensions (reasonable limits)
                    pil_image = PILImage.open(image)
                    width, height = pil_image.size

                    if width > 5000 or height > 5000:
                        raise forms.ValidationError(
                            f"Image dimensions too large ({width}x{height}). "
                            f"Maximum dimensions are 5000x5000 pixels."
                        )

                    if width < 50 or height < 50:
                        raise forms.ValidationError(
                            f"Image too small ({width}x{height}). "
                            f"Minimum dimensions are 50x50 pixels."
                        )

                except Exception as e:
                    raise forms.ValidationError(
                        f"Invalid or corrupted image file. "
                        f"Please try a different image. Error: {str(e)}"
                    )

            except forms.ValidationError:
                raise  # Re-raise validation errors
            except Exception as e:
                raise forms.ValidationError(
                    f"Error processing image: {str(e)}. Please try a different image."
                )

        return image

    class Meta:
        model = DreamItem
        fields = ['title', 'description', 'image', 'image_url', 'category', 'target_date', 'priority', 'notes']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'What do you dream of achieving?'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Describe your dream in detail...'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'image_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://example.com/image.jpg (alternative to file upload)'
            }),
            'category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'target_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'priority': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '10',
                'step': '1'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Additional notes, steps, or thoughts...'
            }),
        }
        help_texts = {
            'image': 'Upload an image of your dream (max 10MB). Supported formats: JPEG, PNG, GIF, WebP. Large images will be automatically resized.',
            'image_url': 'Or paste an image URL instead of uploading a file (e.g., from Unsplash, Pinterest, etc.)',
            'target_date': 'When do you want to achieve this dream? (optional)',
            'priority': 'How important is this dream to you? (1-10 scale)',
        }


class MilestoneForm(forms.ModelForm):
    class Meta:
        model = Milestone
        fields = ['title', 'description', 'target_date', 'order']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Milestone title...'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Brief description of this milestone...'
            }),
            'target_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'step': '1'
            }),
        }
        help_texts = {
            'target_date': 'When do you want to complete this milestone? (optional)',
            'order': 'Order of this milestone in the project (1, 2, 3...)',
        }


class DreamMilestoneForm(forms.ModelForm):
    class Meta:
        model = DreamMilestone
        fields = ['title', 'description', 'target_date', 'order']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Milestone title...'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Brief description of this milestone...'
            }),
            'target_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'step': '1'
            }),
        }
        help_texts = {
            'target_date': 'When do you want to complete this milestone? (optional)',
            'order': 'Order of this milestone in your dream (1, 2, 3...)',
        }
