# Generated by Django 4.2.7 on 2025-07-09 18:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('timeline_app', '0003_projectcategory_projecttag_project_notes_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DreamItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('image_url', models.URLField(blank=True, help_text='URL to an image representing this dream')),
                ('category', models.CharField(choices=[('travel', 'Travel & Adventure'), ('career', 'Career & Business'), ('lifestyle', 'Lifestyle & Home'), ('relationships', 'Relationships & Family'), ('health', 'Health & Fitness'), ('education', 'Education & Skills'), ('material', 'Material Goals'), ('experiences', 'Experiences'), ('other', 'Other')], default='other', max_length=20)),
                ('target_date', models.DateField(blank=True, help_text='When you want to achieve this', null=True)),
                ('priority', models.IntegerField(default=5, help_text='Priority from 1-10')),
                ('is_achieved', models.BooleanField(default=False)),
                ('achieved_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('position_x', models.FloatField(default=0, help_text='X position on dream wall (0-100)')),
                ('position_y', models.FloatField(default=0, help_text='Y position on dream wall (0-100)')),
                ('size_factor', models.FloatField(default=1.0, help_text='Size multiplier (0.5-2.0)')),
                ('rotation', models.FloatField(default=0, help_text='Rotation in degrees (-15 to 15)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-priority', '-created_at'],
            },
        ),
    ]
