{% extends 'base.html' %}
{% load timeline_extras %}

{% block title %}{{ project.title }} - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Project Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">
                                <i class="fas fa-project-diagram"></i> {{ project.title }}
                            </h3>
                            <small>{{ project.get_category_display }} | {{ project.get_status_display }}</small>
                        </div>
                        <div class="btn-group">
                            <a href="{% url 'project_edit' project.id %}" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{% url 'dashboard' %}" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            {% if project.description %}
                                <p class="text-muted">{{ project.description }}</p>
                            {% endif %}
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Start Date:</strong> {{ project.start_date|date:"M d, Y" }}<br>
                                    {% if project.end_date %}
                                        <strong>End Date:</strong> {{ project.end_date|date:"M d, Y" }}<br>
                                    {% endif %}
                                    <strong>Priority:</strong> 
                                    <span class="badge bg-{% if project.priority == 'high' %}danger{% elif project.priority == 'medium' %}warning{% else %}secondary{% endif %}">
                                        {{ project.get_priority_display }}
                                    </span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Progress:</strong> {{ progress_percentage|floatformat:0 }}%<br>
                                    <div class="progress mt-2" style="height: 20px;">
                                        <div class="progress-bar bg-{% if progress_percentage >= 80 %}success{% elif progress_percentage >= 50 %}info{% elif progress_percentage >= 25 %}warning{% else %}danger{% endif %}" 
                                             role="progressbar" 
                                             style="width: {{ progress_percentage }}%"
                                             aria-valuenow="{{ progress_percentage }}" 
                                             aria-valuemin="0" 
                                             aria-valuemax="100">
                                            {{ progress_percentage|floatformat:0 }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="milestone-summary">
                                <h6><i class="fas fa-flag-checkered"></i> Milestone Summary</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="stat-box">
                                            <div class="stat-number text-primary">{{ milestone_stats.total }}</div>
                                            <div class="stat-label">Total</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-box">
                                            <div class="stat-number text-success">{{ milestone_stats.completed }}</div>
                                            <div class="stat-label">Done</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-box">
                                            <div class="stat-number text-warning">{{ milestone_stats.remaining }}</div>
                                            <div class="stat-label">Left</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Milestones Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-flag-checkered"></i> Milestones
                        </h5>
                        <a href="{% url 'milestone_create' project.id %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add Milestone
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if milestones %}
                        <div class="milestone-list">
                            {% for milestone in milestones %}
                                <div class="milestone-item {% if milestone.is_completed %}completed{% endif %} {% if milestone.is_overdue %}overdue{% endif %}">
                                    <div class="milestone-content">
                                        <div class="milestone-checkbox">
                                            <a href="{% url 'milestone_toggle' milestone.id %}" class="milestone-toggle">
                                                {% if milestone.is_completed %}
                                                    <i class="fas fa-check-circle text-success"></i>
                                                {% else %}
                                                    <i class="far fa-circle text-muted"></i>
                                                {% endif %}
                                            </a>
                                        </div>
                                        
                                        <div class="milestone-details">
                                            <h6 class="milestone-title {% if milestone.is_completed %}text-muted{% endif %}">
                                                {{ milestone.title }}
                                                {% if milestone.is_overdue and not milestone.is_completed %}
                                                    <span class="badge bg-danger ms-2">Overdue</span>
                                                {% endif %}
                                            </h6>
                                            {% if milestone.description %}
                                                <p class="milestone-description text-muted">{{ milestone.description }}</p>
                                            {% endif %}
                                            <div class="milestone-meta">
                                                <small class="text-muted">
                                                    Order: {{ milestone.order }}
                                                    {% if milestone.target_date %}
                                                        | Target: {{ milestone.target_date|date:"M d, Y" }}
                                                        {% if not milestone.is_completed and milestone.days_remaining is not None %}
                                                            ({{ milestone.days_remaining }} days left)
                                                        {% endif %}
                                                    {% endif %}
                                                    {% if milestone.completed_date %}
                                                        | Completed: {{ milestone.completed_date|date:"M d, Y" }}
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="milestone-actions">
                                            <div class="btn-group btn-group-sm">
                                                <a href="{% url 'milestone_edit' milestone.id %}" class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'milestone_delete' milestone.id %}" class="btn btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-flag-checkered fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No milestones yet</h5>
                            <p class="text-muted">Break down your project into smaller, manageable milestones to track progress.</p>
                            <a href="{% url 'milestone_create' project.id %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Milestone
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.milestone-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.milestone-item:last-child {
    border-bottom: none;
}

.milestone-item:hover {
    background-color: #f8f9fa;
}

.milestone-item.completed {
    opacity: 0.7;
}

.milestone-item.overdue {
    border-left: 4px solid #dc3545;
}

.milestone-content {
    display: flex;
    align-items: flex-start;
    width: 100%;
    gap: 1rem;
}

.milestone-checkbox {
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.milestone-toggle {
    font-size: 1.2rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.milestone-toggle:hover {
    transform: scale(1.1);
}

.milestone-details {
    flex-grow: 1;
}

.milestone-title {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.milestone-description {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.milestone-meta {
    margin-bottom: 0;
}

.milestone-actions {
    flex-shrink: 0;
}

.milestone-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}
</style>
{% endblock %}
