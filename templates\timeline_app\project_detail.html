{% extends 'base.html' %}
{% load timeline_extras %}

{% block title %}{{ project.title }} - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Project Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">
                                <i class="fas fa-project-diagram"></i> {{ project.title }}
                            </h3>
                            <small>{{ project.get_category_display }} | {{ project.get_status_display }}</small>
                        </div>
                        <div class="btn-group">
                            <a href="{% url 'project_edit' project.id %}" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{% url 'dashboard' %}" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            {% if project.description %}
                                <p class="text-muted">{{ project.description }}</p>
                            {% endif %}
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Start Date:</strong> {{ project.start_date|date:"M d, Y" }}<br>
                                    {% if project.end_date %}
                                        <strong>End Date:</strong> {{ project.end_date|date:"M d, Y" }}<br>
                                    {% endif %}
                                    <strong>Priority:</strong> 
                                    <span class="badge bg-{% if project.priority == 'high' %}danger{% elif project.priority == 'medium' %}warning{% else %}secondary{% endif %}">
                                        {{ project.get_priority_display }}
                                    </span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Progress:</strong> {{ progress_percentage|floatformat:0 }}%<br>
                                    <div class="progress mt-2" style="height: 20px;">
                                        <div class="progress-bar bg-{% if progress_percentage >= 80 %}success{% elif progress_percentage >= 50 %}info{% elif progress_percentage >= 25 %}warning{% else %}danger{% endif %}" 
                                             role="progressbar" 
                                             style="width: {{ progress_percentage }}%"
                                             aria-valuenow="{{ progress_percentage }}" 
                                             aria-valuemin="0" 
                                             aria-valuemax="100">
                                            {{ progress_percentage|floatformat:0 }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="milestone-summary">
                                <h6><i class="fas fa-flag-checkered"></i> Milestone Summary</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="stat-box">
                                            <div class="stat-number text-primary">{{ milestone_stats.total }}</div>
                                            <div class="stat-label">Total</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-box">
                                            <div class="stat-number text-success">{{ milestone_stats.completed }}</div>
                                            <div class="stat-label">Done</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-box">
                                            <div class="stat-number text-warning">{{ milestone_stats.remaining }}</div>
                                            <div class="stat-label">Left</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Smart Suggestions -->
    {% if suggestions %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb"></i> Smart Suggestions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for suggestion in suggestions %}
                            <div class="col-md-6 mb-3">
                                <div class="suggestion-card priority-{{ suggestion.priority }}">
                                    <div class="suggestion-content">
                                        <h6 class="suggestion-title">
                                            {% if suggestion.priority == 'urgent' %}
                                                <i class="fas fa-exclamation-triangle text-danger"></i>
                                            {% elif suggestion.priority == 'high' %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% elif suggestion.priority == 'medium' %}
                                                <i class="fas fa-info-circle text-info"></i>
                                            {% else %}
                                                <i class="fas fa-thumbs-up text-success"></i>
                                            {% endif %}
                                            {{ suggestion.title }}
                                        </h6>
                                        <p class="suggestion-description">{{ suggestion.description }}</p>
                                        {% if suggestion.days_until is not None %}
                                            <small class="text-muted">
                                                {% if suggestion.days_until > 0 %}
                                                    <i class="fas fa-calendar"></i> {{ suggestion.days_until }} days remaining
                                                {% elif suggestion.days_until == 0 %}
                                                    <i class="fas fa-clock text-warning"></i> Due today!
                                                {% else %}
                                                    {% with days_overdue=suggestion.days_until|add:"0" %}
                                                        {% if days_overdue < 0 %}
                                                            <i class="fas fa-exclamation-triangle text-danger"></i> {{ days_overdue|cut:"-" }} days overdue
                                                        {% endif %}
                                                    {% endwith %}
                                                {% endif %}
                                            </small>
                                        {% endif %}
                                    </div>
                                    <div class="suggestion-action">
                                        {% if suggestion.type == 'create_milestones' %}
                                            <a href="{% url 'milestone_create' project.id %}" class="btn btn-sm btn-primary">
                                                {{ suggestion.action }}
                                            </a>
                                        {% elif suggestion.type == 'next_milestone' and project.next_milestone %}
                                            <a href="#milestone-{{ project.next_milestone.id }}" class="btn btn-sm btn-info">
                                                {{ suggestion.action }}
                                            </a>
                                        {% else %}
                                            <button class="btn btn-sm btn-outline-secondary" onclick="dismissSuggestion(this)">
                                                {{ suggestion.action }}
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Milestones Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-flag-checkered"></i> Milestones
                        </h5>
                        <div class="btn-group">
                            <a href="{% url 'milestone_create' project.id %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Add Milestone
                            </a>
                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                                <span class="visually-hidden">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="markAllCompleted()">
                                    <i class="fas fa-check-double text-success"></i> Mark All Complete
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="showMilestoneTemplates()">
                                    <i class="fas fa-magic text-info"></i> Use Template
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportMilestones()">
                                    <i class="fas fa-download text-primary"></i> Export List
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if milestones %}
                        <!-- Milestone Progress Visualization -->
                        <div class="milestone-progress-tracker mb-4">
                            <div class="progress-timeline">
                                {% for milestone in milestones %}
                                    <div class="timeline-item {% if milestone.is_completed %}completed{% elif milestone.is_overdue %}overdue{% else %}pending{% endif %}">
                                        <div class="timeline-marker">
                                            {% if milestone.is_completed %}
                                                <i class="fas fa-check-circle"></i>
                                            {% elif milestone.is_overdue %}
                                                <i class="fas fa-exclamation-circle"></i>
                                            {% else %}
                                                <i class="far fa-circle"></i>
                                            {% endif %}
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">{{ milestone.title }}</div>
                                            {% if milestone.target_date %}
                                                <div class="timeline-date">{{ milestone.target_date|date:"M d" }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="milestone-list">
                            {% for milestone in milestones %}
                                <div id="milestone-{{ milestone.id }}" class="milestone-item {% if milestone.is_completed %}completed{% endif %} {% if milestone.is_overdue %}overdue{% endif %}">
                                    <div class="milestone-content">
                                        <div class="milestone-checkbox">
                                            <a href="{% url 'milestone_toggle' milestone.id %}" class="milestone-toggle">
                                                {% if milestone.is_completed %}
                                                    <i class="fas fa-check-circle text-success"></i>
                                                {% else %}
                                                    <i class="far fa-circle text-muted"></i>
                                                {% endif %}
                                            </a>
                                        </div>
                                        
                                        <div class="milestone-details">
                                            <h6 class="milestone-title {% if milestone.is_completed %}text-muted{% endif %}">
                                                {{ milestone.title }}
                                                {% if milestone.is_overdue and not milestone.is_completed %}
                                                    <span class="badge bg-danger ms-2">Overdue</span>
                                                {% endif %}
                                            </h6>
                                            {% if milestone.description %}
                                                <p class="milestone-description text-muted">{{ milestone.description }}</p>
                                            {% endif %}
                                            <div class="milestone-meta">
                                                <small class="text-muted">
                                                    Order: {{ milestone.order }}
                                                    {% if milestone.target_date %}
                                                        | Target: {{ milestone.target_date|date:"M d, Y" }}
                                                        {% if not milestone.is_completed and milestone.days_remaining is not None %}
                                                            ({{ milestone.days_remaining }} days left)
                                                        {% endif %}
                                                    {% endif %}
                                                    {% if milestone.completed_date %}
                                                        | Completed: {{ milestone.completed_date|date:"M d, Y" }}
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="milestone-actions">
                                            <div class="btn-group btn-group-sm">
                                                <a href="{% url 'milestone_edit' milestone.id %}" class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'milestone_delete' milestone.id %}" class="btn btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-flag-checkered fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No milestones yet</h5>
                            <p class="text-muted">Break down your project into smaller, manageable milestones to track progress.</p>
                            <a href="{% url 'milestone_create' project.id %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Milestone
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.milestone-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.milestone-item:last-child {
    border-bottom: none;
}

.milestone-item:hover {
    background-color: #f8f9fa;
}

.milestone-item.completed {
    opacity: 0.7;
}

.milestone-item.overdue {
    border-left: 4px solid #dc3545;
}

.milestone-content {
    display: flex;
    align-items: flex-start;
    width: 100%;
    gap: 1rem;
}

.milestone-checkbox {
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.milestone-toggle {
    font-size: 1.2rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.milestone-toggle:hover {
    transform: scale(1.1);
}

.milestone-details {
    flex-grow: 1;
}

.milestone-title {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.milestone-description {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.milestone-meta {
    margin-bottom: 0;
}

.milestone-actions {
    flex-shrink: 0;
}

.milestone-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

/* Smart Suggestions */
.suggestion-card {
    background: #fff;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #17a2b8;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.suggestion-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.suggestion-card.priority-urgent {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
}

.suggestion-card.priority-high {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
}

.suggestion-card.priority-medium {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #fff 0%, #f0f9ff 100%);
}

.suggestion-card.priority-low {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
}

.suggestion-content {
    flex-grow: 1;
}

.suggestion-title {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.suggestion-description {
    margin-bottom: 0.25rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.suggestion-action {
    flex-shrink: 0;
    margin-left: 1rem;
}

/* Dark theme support for suggestions */
[data-theme="dark"] .suggestion-card {
    background: #343a40;
    color: #f8f9fa;
}

[data-theme="dark"] .suggestion-card.priority-urgent {
    background: linear-gradient(135deg, #343a40 0%, #4a2c2c 100%);
}

[data-theme="dark"] .suggestion-card.priority-high {
    background: linear-gradient(135deg, #343a40 0%, #4a4a2c 100%);
}

[data-theme="dark"] .suggestion-card.priority-medium {
    background: linear-gradient(135deg, #343a40 0%, #2c3e4a 100%);
}

[data-theme="dark"] .suggestion-card.priority-low {
    background: linear-gradient(135deg, #343a40 0%, #2c4a2c 100%);
}

[data-theme="dark"] .suggestion-title {
    color: #f8f9fa;
}

[data-theme="dark"] .suggestion-description {
    color: #adb5bd;
}

/* Milestone Progress Tracker */
.milestone-progress-tracker {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.progress-timeline {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 1rem 0;
}

.progress-timeline::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, #28a745 0%, #28a745 {{ milestone_stats.progress }}%, #e9ecef {{ milestone_stats.progress }}%, #e9ecef 100%);
    border-radius: 2px;
    z-index: 1;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
    max-width: 120px;
}

.timeline-marker {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
    color: white;
}

.timeline-item.overdue .timeline-marker {
    background: #dc3545;
    color: white;
    animation: pulse 2s infinite;
}

.timeline-item.pending .timeline-marker {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

.timeline-content {
    text-align: center;
    min-height: 60px;
}

.timeline-title {
    font-size: 0.85rem;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.25rem;
    color: #495057;
}

.timeline-date {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
}

.timeline-item.completed .timeline-title {
    color: #28a745;
}

.timeline-item.overdue .timeline-title {
    color: #dc3545;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@media (max-width: 768px) {
    .progress-timeline {
        flex-direction: column;
        gap: 1rem;
    }

    .progress-timeline::before {
        display: none;
    }

    .timeline-item {
        flex-direction: row;
        max-width: none;
        width: 100%;
        justify-content: flex-start;
        gap: 1rem;
    }

    .timeline-content {
        text-align: left;
        min-height: auto;
    }
}

/* Celebration Animation */
@keyframes celebrate {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.2) rotate(5deg); }
    50% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.2) rotate(5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

.milestone-celebrate {
    animation: celebrate 0.6s ease-in-out;
}

/* Confetti Effect */
.confetti {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9999;
}

.confetti-piece {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ff6b6b;
    animation: confetti-fall 3s linear infinite;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}
</style>

<script>
// Milestone completion celebration
function celebrateMilestone(milestoneElement) {
    // Add celebration class
    milestoneElement.classList.add('milestone-celebrate');

    // Create confetti effect
    createConfetti();

    // Remove celebration class after animation
    setTimeout(() => {
        milestoneElement.classList.remove('milestone-celebrate');
    }, 600);
}

function createConfetti() {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
    const confettiContainer = document.createElement('div');
    confettiContainer.className = 'confetti';
    document.body.appendChild(confettiContainer);

    for (let i = 0; i < 50; i++) {
        const confettiPiece = document.createElement('div');
        confettiPiece.className = 'confetti-piece';
        confettiPiece.style.left = Math.random() * 100 + '%';
        confettiPiece.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confettiPiece.style.animationDelay = Math.random() * 3 + 's';
        confettiPiece.style.animationDuration = (Math.random() * 3 + 2) + 's';
        confettiContainer.appendChild(confettiPiece);
    }

    // Remove confetti after animation
    setTimeout(() => {
        document.body.removeChild(confettiContainer);
    }, 5000);
}

// Add click handlers for milestone toggles
document.addEventListener('DOMContentLoaded', function() {
    const milestoneToggles = document.querySelectorAll('.milestone-toggle');
    milestoneToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            const milestoneItem = this.closest('.milestone-item');
            const isCompleting = this.querySelector('.far.fa-circle');

            if (isCompleting) {
                e.preventDefault();
                celebrateMilestone(milestoneItem);

                // Proceed with the actual toggle after celebration
                setTimeout(() => {
                    window.location.href = this.href;
                }, 800);
            }
        });
    });
});

// Dismiss suggestion function
function dismissSuggestion(button) {
    const suggestionCard = button.closest('.suggestion-card');
    suggestionCard.style.transition = 'all 0.3s ease';
    suggestionCard.style.opacity = '0';
    suggestionCard.style.transform = 'translateX(100%)';

    setTimeout(() => {
        suggestionCard.remove();
    }, 300);
}
</script>
{% endblock %}
