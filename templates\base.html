<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TimeLine - Life & Project Management{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{% url 'landing' %}">
                <i class="fas fa-clock"></i> TimeLine
            </a>

            {% if user.is_authenticated %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'analytics_dashboard' %}active{% endif %}" href="{% url 'analytics_dashboard' %}">
                            <i class="fas fa-chart-line"></i> Analytics
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.url_name in 'weeks_view,life_timeline,completed_projects' %}active{% endif %}" href="#" id="timelineDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-timeline"></i> Timeline
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'weeks_view' %}"><i class="fas fa-calendar-week"></i> Life in Weeks</a></li>
                            <li><a class="dropdown-item" href="{% url 'life_timeline' %}"><i class="fas fa-road"></i> Life Timeline</a></li>
                            <li><a class="dropdown-item" href="{% url 'completed_projects' %}"><i class="fas fa-trophy"></i> Completed Goals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'dream' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'dream_wall' %}">
                            <i class="fas fa-star"></i> Dream Wall
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'project_create' %}active{% endif %}" href="{% url 'project_create' %}">
                            <i class="fas fa-plus"></i> Add Project
                        </a>
                    </li>
                </ul>

                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'profile_edit' %}"><i class="fas fa-edit"></i> Edit Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="toggleTheme()"><i class="fas fa-moon theme-icon-dark"></i><i class="fas fa-sun theme-icon-light" style="display: none;"></i> Toggle Theme</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </nav>

    <main class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}
        {% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/main.js' %}"></script>

    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update theme icons
            const darkIcons = document.querySelectorAll('.theme-icon-dark');
            const lightIcons = document.querySelectorAll('.theme-icon-light');

            if (newTheme === 'dark') {
                darkIcons.forEach(icon => icon.style.display = 'none');
                lightIcons.forEach(icon => icon.style.display = 'inline');
            } else {
                darkIcons.forEach(icon => icon.style.display = 'inline');
                lightIcons.forEach(icon => icon.style.display = 'none');
            }
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            // Update theme icons based on current theme
            const darkIcons = document.querySelectorAll('.theme-icon-dark');
            const lightIcons = document.querySelectorAll('.theme-icon-light');

            if (savedTheme === 'dark') {
                darkIcons.forEach(icon => icon.style.display = 'none');
                lightIcons.forEach(icon => icon.style.display = 'inline');
            } else {
                darkIcons.forEach(icon => icon.style.display = 'inline');
                lightIcons.forEach(icon => icon.style.display = 'none');
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Only activate shortcuts when not typing in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

            // Ctrl/Cmd + shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n': // Ctrl+N - New Project
                        e.preventDefault();
                        window.location.href = '{% url "project_create" %}';
                        break;
                    case 'd': // Ctrl+D - Dashboard
                        e.preventDefault();
                        window.location.href = '{% url "dashboard" %}';
                        break;
                    case 'w': // Ctrl+W - Dream Wall
                        e.preventDefault();
                        window.location.href = '{% url "dream_wall" %}';
                        break;
                }
            }

            // Single key shortcuts
            switch(e.key) {
                case 't': // T - Toggle Theme
                    e.preventDefault();
                    toggleTheme();
                    break;
                case '?': // ? - Show shortcuts help
                    e.preventDefault();
                    showKeyboardShortcuts();
                    break;
            }
        });

        // Show keyboard shortcuts modal
        function showKeyboardShortcuts() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">⌨️ Keyboard Shortcuts</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-6">
                                    <h6>Navigation</h6>
                                    <ul class="list-unstyled">
                                        <li><kbd>Ctrl</kbd> + <kbd>D</kbd> Dashboard</li>
                                        <li><kbd>Ctrl</kbd> + <kbd>W</kbd> Dream Wall</li>
                                        <li><kbd>Ctrl</kbd> + <kbd>N</kbd> New Project</li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <h6>Actions</h6>
                                    <ul class="list-unstyled">
                                        <li><kbd>T</kbd> Toggle Theme</li>
                                        <li><kbd>?</kbd> Show Shortcuts</li>
                                        <li><kbd>Esc</kbd> Close Modals</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
