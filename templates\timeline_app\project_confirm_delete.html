{% extends 'base.html' %}

{% block title %}Delete Project - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Confirm Deletion
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-warning"></i> Warning</h6>
                    <p class="mb-0">This action cannot be undone. All project data will be permanently deleted.</p>
                </div>
                
                <h5>Are you sure you want to delete this project?</h5>
                
                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">{{ project.title }}</h6>
                        {% if project.description %}
                            <p class="card-text">{{ project.description|truncatewords:20 }}</p>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">
                                    <strong>Priority:</strong> {{ project.get_priority_display }}
                                </small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">
                                    <strong>Status:</strong> {{ project.get_status_display }}
                                </small>
                            </div>
                        </div>
                        
                        {% if project.deadline %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>Deadline:</strong> {{ project.deadline|date:"M d, Y" }}
                                </small>
                            </div>
                        {% endif %}
                        
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Created:</strong> {{ project.created_at|date:"M d, Y" }}
                            </small>
                        </div>
                    </div>
                </div>
                
                <form method="post" class="mt-4">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Delete Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
