# Generated by Django 4.2.7 on 2025-07-09 19:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('timeline_app', '0004_dreamitem'),
    ]

    operations = [
        migrations.AddField(
            model_name='dreamitem',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload an image of your dream', null=True, upload_to='dream_images/'),
        ),
        migrations.AlterField(
            model_name='dreamitem',
            name='image_url',
            field=models.URLField(blank=True, help_text='Or provide a URL to an image representing this dream'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='timezone',
            field=models.CharField(choices=[('UTC', 'UTC'), ('US/Eastern', 'US Eastern (New York)'), ('US/Central', 'US Central (Chicago)'), ('US/Mountain', 'US Mountain (Denver)'), ('US/Pacific', 'US Pacific (Los Angeles)'), ('America/Toronto', 'Toronto'), ('America/Vancouver', 'Vancouver'), ('America/Mexico_City', 'Mexico City'), ('Europe/London', 'London (GMT)'), ('Europe/Paris', 'Paris (CET)'), ('Europe/Berlin', 'Berlin (CET)'), ('Europe/Rome', 'Rome (CET)'), ('Europe/Madrid', 'Madrid (CET)'), ('Europe/Amsterdam', 'Amsterdam (CET)'), ('Europe/Brussels', 'Brussels (CET)'), ('Europe/Zurich', 'Zurich (CET)'), ('Europe/Vienna', 'Vienna (CET)'), ('Europe/Prague', 'Prague (CET)'), ('Europe/Warsaw', 'Warsaw (CET)'), ('Europe/Stockholm', 'Stockholm (CET)'), ('Europe/Oslo', 'Oslo (CET)'), ('Europe/Copenhagen', 'Copenhagen (CET)'), ('Europe/Helsinki', 'Helsinki (EET)'), ('Europe/Sofia', 'Sofia, Bulgaria (EET)'), ('Europe/Bucharest', 'Bucharest, Romania (EET)'), ('Europe/Athens', 'Athens, Greece (EET)'), ('Europe/Kiev', 'Kiev, Ukraine (EET)'), ('Europe/Riga', 'Riga, Latvia (EET)'), ('Europe/Vilnius', 'Vilnius, Lithuania (EET)'), ('Europe/Tallinn', 'Tallinn, Estonia (EET)'), ('Europe/Belgrade', 'Belgrade, Serbia (CET)'), ('Europe/Zagreb', 'Zagreb, Croatia (CET)'), ('Europe/Ljubljana', 'Ljubljana, Slovenia (CET)'), ('Europe/Budapest', 'Budapest, Hungary (CET)'), ('Europe/Bratislava', 'Bratislava, Slovakia (CET)'), ('Europe/Moscow', 'Moscow (MSK)'), ('Europe/Volgograd', 'Volgograd (MSK)'), ('Asia/Yekaterinburg', 'Yekaterinburg (YEKT)'), ('Asia/Tokyo', 'Tokyo (JST)'), ('Asia/Shanghai', 'Shanghai (CST)'), ('Asia/Hong_Kong', 'Hong Kong (HKT)'), ('Asia/Singapore', 'Singapore (SGT)'), ('Asia/Seoul', 'Seoul (KST)'), ('Asia/Bangkok', 'Bangkok (ICT)'), ('Asia/Dubai', 'Dubai (GST)'), ('Asia/Istanbul', 'Istanbul (TRT)'), ('Australia/Sydney', 'Sydney (AEDT)'), ('Australia/Melbourne', 'Melbourne (AEDT)'), ('Australia/Perth', 'Perth (AWST)'), ('Pacific/Auckland', 'Auckland (NZDT)'), ('Africa/Cairo', 'Cairo (EET)'), ('Africa/Johannesburg', 'Johannesburg (SAST)'), ('America/Sao_Paulo', 'São Paulo (BRT)'), ('America/Buenos_Aires', 'Buenos Aires (ART)')], default='UTC', max_length=50),
        ),
    ]
