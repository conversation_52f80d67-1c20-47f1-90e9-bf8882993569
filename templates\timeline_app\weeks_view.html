{% extends 'base.html' %}

{% block title %}Life in Weeks - TimeLine{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-calendar-alt text-primary"></i> Your Life in Weeks
                </h4>
                <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3 text-center">
                        <div class="stat-card">
                            <h3 class="text-success">{{ weeks_lived }}</h3>
                            <p class="text-muted mb-0">Weeks Lived</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stat-card">
                            <h3 class="text-warning">{{ weeks_remaining }}</h3>
                            <p class="text-muted mb-0">Weeks Remaining</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stat-card">
                            <h3 class="text-info">{{ total_weeks }}</h3>
                            <p class="text-muted mb-0">Total Weeks (100 years)</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stat-card">
                            <h3 class="text-primary">{{ profile.age }}</h3>
                            <p class="text-muted mb-0">Current Age</p>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> How to Read This Chart</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><span class="badge bg-success me-2"></span> Green squares = Weeks you've lived</p>
                            <p class="mb-0"><span class="badge bg-light text-dark me-2"></span> Gray squares = Future weeks</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1">Each row represents one year (52 weeks)</p>
                            <p class="mb-0">Each square represents one week of your life</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-th"></i> Life Grid (100 Years × 52 Weeks)
                </h5>
            </div>
            <div class="card-body">
                <div class="weeks-container">
                    {% for year_data in weeks_data %}
                        {% if year_data.year <= 100 %}
                            <div class="year-row mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="year-label me-3" style="min-width: 60px;">
                                        Year {{ year_data.year }}
                                        {% if year_data.year == profile.age|add:1 %}
                                            <span class="badge bg-primary">Current</span>
                                        {% endif %}
                                    </div>
                                    <div class="weeks-grid">
                                        {% for week in year_data.weeks %}
                                            <div class="week-cell {% if week.is_lived %}lived{% else %}future{% endif %}"
                                                 data-bs-toggle="tooltip"
                                                 data-bs-placement="top"
                                                 title="Year {{ week.year }}, Week {{ week.week }}{% if week.is_lived %} (Lived){% else %} (Future){% endif %}">
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
                
                <div class="mt-4 text-center">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-hourglass-half"></i> Time Perspective</h6>
                        <p class="mb-2">
                            You have lived <strong>{{ weeks_lived }}</strong> weeks and have approximately 
                            <strong>{{ weeks_remaining }}</strong> weeks remaining.
                        </p>
                        <p class="mb-0">
                            That's about <strong>{{ weeks_remaining|floatformat:0 }}</strong> weeks to achieve your dreams, 
                            spend time with loved ones, and make a difference in the world.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb"></i> Reflection Questions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Looking Back:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check-circle text-success me-2"></i> What are you most proud of so far?</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i> What experiences shaped you the most?</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i> What would you do differently?</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Looking Forward:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary me-2"></i> What do you want to accomplish?</li>
                            <li><i class="fas fa-arrow-right text-primary me-2"></i> Who do you want to become?</li>
                            <li><i class="fas fa-arrow-right text-primary me-2"></i> How will you make each week count?</li>
                        </ul>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{% url 'project_create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Create a New Project
                    </a>
                    <a href="{% url 'dashboard' %}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips for week cells
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Add animation to week cells
        const weekCells = document.querySelectorAll('.week-cell');
        weekCells.forEach((cell, index) => {
            setTimeout(() => {
                cell.style.opacity = '1';
                cell.style.transform = 'scale(1)';
            }, index * 2);
        });
    });
</script>

<style>
    .stat-card {
        padding: 1rem;
        border-radius: 8px;
        background-color: #f8f9fa;
        margin-bottom: 1rem;
    }
    
    .weeks-container {
        max-height: 70vh;
        overflow-y: auto;
        padding: 1rem;
        background-color: #fafafa;
        border-radius: 8px;
    }
    
    .year-row {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 0.5rem;
    }
    
    .weeks-grid {
        display: grid;
        grid-template-columns: repeat(52, 1fr);
        gap: 1px;
        flex-grow: 1;
    }
    
    .week-cell {
        width: 10px;
        height: 10px;
        border-radius: 1px;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
    }
    
    .week-cell.lived {
        background-color: #28a745;
    }
    
    .week-cell.future {
        background-color: #e9ecef;
        border: 1px solid #dee2e6;
    }
    
    .week-cell:hover {
        transform: scale(1.5);
        z-index: 10;
        position: relative;
    }
    
    @media (max-width: 768px) {
        .weeks-grid {
            grid-template-columns: repeat(26, 1fr);
        }
        
        .week-cell {
            width: 8px;
            height: 8px;
        }
        
        .year-label {
            font-size: 0.8rem;
            min-width: 50px !important;
        }
    }
</style>
{% endblock %}
