from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from datetime import date, datetime
from .models import UserProfile, Project, DreamItem
from .forms import UserProfileForm, ProjectForm, CustomUserCreationForm, DreamItemForm


def landing_page(request):
    """Landing page with login/register options"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    return render(request, 'timeline_app/landing.html')


def register_view(request):
    """User registration view"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Create user profile
            UserProfile.objects.create(user=user)
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}!')
            return redirect('login')
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})


@login_required
def dashboard(request):
    """Main dashboard after login"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    projects = Project.objects.filter(user=request.user).order_by('-updated_at')

    # Get analytics data
    analytics = profile.get_analytics_data()

    # Get recent projects (last 5)
    recent_projects = projects[:5]

    # Get upcoming deadlines (next 7 days)
    from datetime import timedelta
    next_week = date.today() + timedelta(days=7)
    upcoming_deadlines = projects.filter(
        deadline__lte=next_week,
        deadline__gte=date.today(),
        status__in=['not_started', 'in_progress']
    ).order_by('deadline')[:3]

    context = {
        'profile': profile,
        'projects': projects,
        'recent_projects': recent_projects,
        'upcoming_deadlines': upcoming_deadlines,
        'analytics': analytics,
        'current_time': timezone.now(),
    }
    return render(request, 'timeline_app/dashboard.html', context)


@login_required
def profile_edit(request):
    """Edit user profile"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=profile, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('dashboard')
    else:
        form = UserProfileForm(instance=profile, user=request.user)

    return render(request, 'timeline_app/profile_edit.html', {'form': form, 'profile': profile})


@login_required
def weeks_view(request):
    """Detailed weeks view of life timeline"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if not profile.birth_date:
        messages.warning(request, 'Please set your birth date in your profile first.')
        return redirect('profile_edit')

    # Calculate weeks data
    weeks_data = []
    if profile.birth_date:
        total_weeks = int(100 * 52.18)  # Approximate weeks in 100 years
        weeks_lived = profile.weeks_lived

        # Create grid of weeks (52 weeks per year, 100 years)
        for year in range(100):
            year_weeks = []
            for week in range(52):
                week_number = year * 52 + week
                is_lived = week_number < weeks_lived
                year_weeks.append({
                    'week': week + 1,
                    'is_lived': is_lived,
                    'year': year + 1
                })
            weeks_data.append({
                'year': year + 1,
                'weeks': year_weeks
            })

    context = {
        'profile': profile,
        'weeks_data': weeks_data,
        'total_weeks': int(100 * 52.18),
        'weeks_lived': profile.weeks_lived,
        'weeks_remaining': profile.weeks_remaining,
    }
    return render(request, 'timeline_app/weeks_view.html', context)


@login_required
def project_create(request):
    """Create a new project"""
    if request.method == 'POST':
        form = ProjectForm(request.POST)
        if form.is_valid():
            project = form.save(commit=False)
            project.user = request.user
            project.save()
            messages.success(request, 'Project created successfully!')
            return redirect('dashboard')
    else:
        form = ProjectForm()

    return render(request, 'timeline_app/project_form.html', {'form': form, 'title': 'Create Project'})


@login_required
def project_edit(request, project_id):
    """Edit an existing project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        form = ProjectForm(request.POST, instance=project)
        if form.is_valid():
            form.save()
            messages.success(request, 'Project updated successfully!')
            return redirect('dashboard')
    else:
        form = ProjectForm(instance=project)

    return render(request, 'timeline_app/project_form.html', {'form': form, 'title': 'Edit Project', 'project': project})


@login_required
def project_delete(request, project_id):
    """Delete a project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        project.delete()
        messages.success(request, 'Project deleted successfully!')
        return redirect('dashboard')

    return render(request, 'timeline_app/project_confirm_delete.html', {'project': project})


@login_required
def completed_projects(request):
    """View for completed projects/goals"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    completed = Project.objects.filter(user=request.user, status='completed').order_by('-updated_at')

    # Group by completion year
    projects_by_year = {}
    for project in completed:
        year = project.updated_at.year
        if year not in projects_by_year:
            projects_by_year[year] = []
        projects_by_year[year].append(project)

    # Calculate achievements
    total_completed = completed.count()
    completion_streak = calculate_completion_streak(request.user)

    context = {
        'profile': profile,
        'completed_projects': completed,
        'projects_by_year': dict(sorted(projects_by_year.items(), reverse=True)),
        'total_completed': total_completed,
        'completion_streak': completion_streak,
    }
    return render(request, 'timeline_app/completed_projects.html', context)


@login_required
def life_timeline_view(request):
    """Interactive life timeline with projects mapped to life events"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if not profile.birth_date:
        messages.warning(request, 'Please set your birth date in your profile first.')
        return redirect('profile_edit')

    # Get all projects
    projects = Project.objects.filter(user=request.user).order_by('created_at')

    # Calculate life timeline data
    life_events = []
    current_year = date.today().year
    birth_year = profile.birth_date.year

    for year in range(birth_year, current_year + 1):
        age = year - birth_year
        year_projects = projects.filter(created_at__year=year)

        life_events.append({
            'year': year,
            'age': age,
            'projects': year_projects,
            'is_current': year == current_year
        })

    context = {
        'profile': profile,
        'life_events': life_events,
        'projects': projects,
    }
    return render(request, 'timeline_app/life_timeline.html', context)


def calculate_completion_streak(user):
    """Calculate current completion streak"""
    from datetime import timedelta

    completed_projects = Project.objects.filter(
        user=user,
        status='completed'
    ).order_by('-updated_at')

    if not completed_projects:
        return 0

    streak = 0
    last_completion = None

    for project in completed_projects:
        if last_completion is None:
            streak = 1
            last_completion = project.updated_at.date()
        else:
            days_diff = (last_completion - project.updated_at.date()).days
            if days_diff <= 7:  # Within a week
                streak += 1
                last_completion = project.updated_at.date()
            else:
                break

    return streak


@login_required
def dream_wall(request):
    """Dream wall/vision board view"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    dreams = DreamItem.objects.filter(user=request.user)

    # Separate achieved and unachieved dreams
    active_dreams = dreams.filter(is_achieved=False)
    achieved_dreams = dreams.filter(is_achieved=True)

    context = {
        'profile': profile,
        'active_dreams': active_dreams,
        'achieved_dreams': achieved_dreams,
        'total_dreams': dreams.count(),
        'achieved_count': achieved_dreams.count(),
    }
    return render(request, 'timeline_app/dream_wall.html', context)


@login_required
def dream_item_create(request):
    """Create a new dream item"""
    if request.method == 'POST':
        form = DreamItemForm(request.POST, request.FILES)

        # Debug: Log what we received
        print(f"POST data: {request.POST}")
        print(f"FILES data: {request.FILES}")

        if form.is_valid():
            dream = form.save(commit=False)
            dream.user = request.user
            dream.save()

            # Debug: Log what was saved
            print(f"Dream saved: {dream.title}")
            if dream.image:
                print(f"Image saved: {dream.image.url}")
            if dream.image_url:
                print(f"Image URL saved: {dream.image_url}")

            messages.success(request, f'Dream "{dream.title}" added to your vision board!')
            return redirect('dream_wall')
        else:
            # Add debug info for form errors
            print(f"Form errors: {form.errors}")
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = DreamItemForm()

    return render(request, 'timeline_app/dream_form.html', {
        'form': form,
        'title': 'Add New Dream'
    })


@login_required
def dream_item_edit(request, dream_id):
    """Edit a dream item"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)

    if request.method == 'POST':
        form = DreamItemForm(request.POST, request.FILES, instance=dream)

        # Debug: Log what we received
        print(f"Edit POST data: {request.POST}")
        print(f"Edit FILES data: {request.FILES}")

        if form.is_valid():
            updated_dream = form.save()

            # Debug: Log what was saved
            print(f"Dream updated: {updated_dream.title}")
            if updated_dream.image:
                print(f"Image updated: {updated_dream.image.url}")
            if updated_dream.image_url:
                print(f"Image URL updated: {updated_dream.image_url}")

            messages.success(request, f'Dream "{dream.title}" updated!')
            return redirect('dream_wall')
        else:
            # Add debug info for form errors
            print(f"Edit form errors: {form.errors}")
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = DreamItemForm(instance=dream)

    return render(request, 'timeline_app/dream_form.html', {
        'form': form,
        'dream': dream,
        'title': 'Edit Dream'
    })


@login_required
def dream_item_delete(request, dream_id):
    """Delete a dream item"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)

    if request.method == 'POST':
        dream_title = dream.title
        dream.delete()
        messages.success(request, f'Dream "{dream_title}" removed from your vision board.')
        return redirect('dream_wall')

    return render(request, 'timeline_app/dream_confirm_delete.html', {'dream': dream})


@login_required
def dream_item_achieve(request, dream_id):
    """Mark a dream as achieved"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)

    if request.method == 'POST':
        dream.is_achieved = True
        dream.save()
        messages.success(request, f'Congratulations! You achieved "{dream.title}"! 🎉')
        return redirect('dream_wall')

    return render(request, 'timeline_app/dream_achieve.html', {'dream': dream})


@login_required
def randomize_dream_positions(request):
    """Randomize positions of all dream items"""
    if request.method == 'POST':
        import random
        dreams = DreamItem.objects.filter(user=request.user, is_achieved=False)

        for dream in dreams:
            dream.position_x = random.uniform(5, 95)
            dream.position_y = random.uniform(5, 95)
            dream.size_factor = random.uniform(0.8, 1.5)
            dream.rotation = random.uniform(-10, 10)
            dream.save()

        return JsonResponse({'status': 'success', 'message': 'Dream positions randomized!'})

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})


@login_required
def get_current_time(request):
    """API endpoint to get current time"""
    from django.utils import timezone as django_timezone

    # Get user's timezone preference
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    # Get current time (for now in UTC, can be enhanced with pytz later)
    current_time = django_timezone.now()

    return JsonResponse({
        'current_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
        'date': current_time.strftime('%B %d, %Y'),
        'time': current_time.strftime('%H:%M:%S'),
        'timezone': profile.timezone,
        'utc_offset': '+0000'  # UTC for now
    })
