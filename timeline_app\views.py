from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, datetime, timedelta
from .models import UserProfile, Project, DreamItem, Milestone, DreamMilestone, MilestoneTemplate, Notification
from .notification_service import NotificationService
from .forms import UserProfileForm, ProjectForm, CustomUserCreationForm, DreamItemForm, MilestoneForm, DreamMilestoneForm


def landing_page(request):
    """Landing page with login/register options"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    return render(request, 'timeline_app/landing.html')


def register_view(request):
    """User registration view"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Create user profile
            UserProfile.objects.create(user=user)
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}!')
            return redirect('login')
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})


@login_required
def dashboard(request):
    """Main dashboard after login"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    projects = Project.objects.filter(user=request.user).order_by('-updated_at')

    # Get analytics data
    analytics = profile.get_analytics_data()

    # Get recent projects (last 5)
    recent_projects = projects[:5]

    # Get upcoming deadlines (next 7 days)
    from datetime import timedelta
    next_week = date.today() + timedelta(days=7)
    upcoming_deadlines = projects.filter(
        deadline__lte=next_week,
        deadline__gte=date.today(),
        status__in=['not_started', 'in_progress']
    ).order_by('deadline')[:3]

    # Get current time in user's timezone
    utc_time = timezone.now()
    if profile.timezone:
        try:
            import pytz
            user_tz = pytz.timezone(profile.timezone)
            current_time = utc_time.astimezone(user_tz)
        except:
            current_time = utc_time
    else:
        current_time = utc_time

    # Get popular milestone templates
    popular_templates = MilestoneTemplate.objects.filter(is_active=True).order_by('category', 'name')[:6]

    # Get recent notifications using the custom manager
    recent_notifications = Notification.objects.for_user(request.user).active().order_by('-created_at')[:5]

    context = {
        'profile': profile,
        'projects': projects,
        'recent_projects': recent_projects,
        'upcoming_deadlines': upcoming_deadlines,
        'analytics': analytics,
        'current_time': current_time,
        'popular_templates': popular_templates,
        'recent_notifications': recent_notifications,
    }
    return render(request, 'timeline_app/dashboard.html', context)


@login_required
def profile_edit(request):
    """Edit user profile"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=profile, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('dashboard')
    else:
        form = UserProfileForm(instance=profile, user=request.user)

    return render(request, 'timeline_app/profile_edit.html', {'form': form, 'profile': profile})


@login_required
def weeks_view(request):
    """Detailed weeks view of life timeline"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if not profile.birth_date:
        messages.warning(request, 'Please set your birth date in your profile first.')
        return redirect('profile_edit')

    # Calculate weeks data
    weeks_data = []
    if profile.birth_date:
        total_weeks = int(100 * 52.18)  # Approximate weeks in 100 years
        weeks_lived = profile.weeks_lived

        # Create grid of weeks (52 weeks per year, 100 years)
        for year in range(100):
            year_weeks = []
            for week in range(52):
                week_number = year * 52 + week
                is_lived = week_number < weeks_lived
                year_weeks.append({
                    'week': week + 1,
                    'is_lived': is_lived,
                    'year': year + 1
                })
            weeks_data.append({
                'year': year + 1,
                'weeks': year_weeks
            })

    context = {
        'profile': profile,
        'weeks_data': weeks_data,
        'total_weeks': int(100 * 52.18),
        'weeks_lived': profile.weeks_lived,
        'weeks_remaining': profile.weeks_remaining,
    }
    return render(request, 'timeline_app/weeks_view.html', context)


@login_required
def project_create(request):
    """Create a new project"""
    if request.method == 'POST':
        form = ProjectForm(request.POST)
        if form.is_valid():
            project = form.save(commit=False)
            project.user = request.user
            project.save()
            messages.success(request, 'Project created successfully!')
            return redirect('dashboard')
    else:
        form = ProjectForm()

    return render(request, 'timeline_app/project_form.html', {'form': form, 'title': 'Create Project'})


@login_required
def project_edit(request, project_id):
    """Edit an existing project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        form = ProjectForm(request.POST, instance=project)
        if form.is_valid():
            form.save()
            messages.success(request, 'Project updated successfully!')
            return redirect('dashboard')
    else:
        form = ProjectForm(instance=project)

    return render(request, 'timeline_app/project_form.html', {'form': form, 'title': 'Edit Project', 'project': project})


@login_required
def project_detail(request, project_id):
    """View project details with milestones"""
    project = get_object_or_404(Project, id=project_id, user=request.user)
    milestones = project.milestones.all()

    context = {
        'project': project,
        'milestones': milestones,
        'milestone_stats': project.milestone_stats,
        'progress_percentage': project.get_progress_percentage(),
        'suggestions': project.get_milestone_suggestions(),
    }

    return render(request, 'timeline_app/project_detail.html', context)


@login_required
def project_delete(request, project_id):
    """Delete a project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        project.delete()
        messages.success(request, 'Project deleted successfully!')
        return redirect('dashboard')

    return render(request, 'timeline_app/project_confirm_delete.html', {'project': project})


@login_required
def completed_projects(request):
    """View for completed projects/goals"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    completed = Project.objects.filter(user=request.user, status='completed').order_by('-updated_at')

    # Group by completion year
    projects_by_year = {}
    for project in completed:
        year = project.updated_at.year
        if year not in projects_by_year:
            projects_by_year[year] = []
        projects_by_year[year].append(project)

    # Calculate achievements
    total_completed = completed.count()
    completion_streak = calculate_completion_streak(request.user)

    context = {
        'profile': profile,
        'completed_projects': completed,
        'projects_by_year': dict(sorted(projects_by_year.items(), reverse=True)),
        'total_completed': total_completed,
        'completion_streak': completion_streak,
    }
    return render(request, 'timeline_app/completed_projects.html', context)


@login_required
def life_timeline_view(request):
    """Interactive life timeline with projects mapped to life events"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if not profile.birth_date:
        messages.warning(request, 'Please set your birth date in your profile first.')
        return redirect('profile_edit')

    # Get all projects
    projects = Project.objects.filter(user=request.user).order_by('created_at')

    # Calculate life timeline data
    life_events = []
    current_year = date.today().year
    birth_year = profile.birth_date.year

    for year in range(birth_year, current_year + 1):
        age = year - birth_year
        year_projects = projects.filter(created_at__year=year)

        life_events.append({
            'year': year,
            'age': age,
            'projects': year_projects,
            'is_current': year == current_year
        })

    context = {
        'profile': profile,
        'life_events': life_events,
        'projects': projects,
    }
    return render(request, 'timeline_app/life_timeline.html', context)


def calculate_completion_streak(user):
    """Calculate current completion streak"""
    from datetime import timedelta

    completed_projects = Project.objects.filter(
        user=user,
        status='completed'
    ).order_by('-updated_at')

    if not completed_projects:
        return 0

    streak = 0
    last_completion = None

    for project in completed_projects:
        if last_completion is None:
            streak = 1
            last_completion = project.updated_at.date()
        else:
            days_diff = (last_completion - project.updated_at.date()).days
            if days_diff <= 7:  # Within a week
                streak += 1
                last_completion = project.updated_at.date()
            else:
                break

    return streak


@login_required
def dream_wall(request):
    """Dream wall/vision board view"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    dreams = DreamItem.objects.filter(user=request.user)

    # Separate achieved and unachieved dreams
    active_dreams = dreams.filter(is_achieved=False)
    achieved_dreams = dreams.filter(is_achieved=True)

    context = {
        'profile': profile,
        'active_dreams': active_dreams,
        'achieved_dreams': achieved_dreams,
        'total_dreams': dreams.count(),
        'achieved_count': achieved_dreams.count(),
    }
    return render(request, 'timeline_app/dream_wall.html', context)


@login_required
def dream_item_create(request):
    """Create a new dream item"""
    if request.method == 'POST':
        form = DreamItemForm(request.POST, request.FILES)

        if form.is_valid():
            dream = form.save(commit=False)
            dream.user = request.user
            dream.save()
            messages.success(request, f'Dream "{dream.title}" added to your vision board!')
            return redirect('dream_wall')
        else:
            # Show form errors to user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = DreamItemForm()

    return render(request, 'timeline_app/dream_form.html', {
        'form': form,
        'title': 'Add New Dream'
    })


@login_required
def dream_item_edit(request, dream_id):
    """Edit a dream item"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)

    if request.method == 'POST':
        form = DreamItemForm(request.POST, request.FILES, instance=dream)

        if form.is_valid():
            updated_dream = form.save()
            messages.success(request, f'Dream "{dream.title}" updated!')
            return redirect('dream_wall')
        else:
            # Show form errors to user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = DreamItemForm(instance=dream)

    return render(request, 'timeline_app/dream_form.html', {
        'form': form,
        'dream': dream,
        'title': 'Edit Dream'
    })


@login_required
def dream_item_delete(request, dream_id):
    """Delete a dream item"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)

    if request.method == 'POST':
        dream_title = dream.title
        dream.delete()
        messages.success(request, f'Dream "{dream_title}" removed from your vision board.')
        return redirect('dream_wall')

    return render(request, 'timeline_app/dream_confirm_delete.html', {'dream': dream})


@login_required
def dream_detail(request, dream_id):
    """View dream details with milestones"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)
    milestones = dream.milestones.all()

    context = {
        'dream': dream,
        'milestones': milestones,
        'milestone_stats': dream.milestone_stats,
        'progress_percentage': dream.progress_percentage,
    }

    return render(request, 'timeline_app/dream_detail.html', context)


@login_required
def dream_item_achieve(request, dream_id):
    """Mark a dream as achieved"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)

    if request.method == 'POST':
        dream.is_achieved = True
        dream.save()
        messages.success(request, f'Congratulations! You achieved "{dream.title}"! 🎉')
        return redirect('dream_wall')

    return render(request, 'timeline_app/dream_achieve.html', {'dream': dream})


@login_required
def randomize_dream_positions(request):
    """Randomize positions of all dream items"""
    if request.method == 'POST':
        import random
        dreams = DreamItem.objects.filter(user=request.user, is_achieved=False)

        for dream in dreams:
            dream.position_x = random.uniform(5, 95)
            dream.position_y = random.uniform(5, 95)
            dream.size_factor = random.uniform(0.8, 1.5)
            dream.rotation = random.uniform(-10, 10)
            dream.save()

        return JsonResponse({'status': 'success', 'message': 'Dream positions randomized!'})

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})


@login_required
def get_current_time(request):
    """API endpoint to get current time in user's timezone"""
    from django.utils import timezone as django_timezone
    import pytz

    # Get user's timezone preference
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    # Get current time in UTC
    utc_time = django_timezone.now()

    # Convert to user's timezone if set
    if profile.timezone:
        try:
            user_tz = pytz.timezone(profile.timezone)
            local_time = utc_time.astimezone(user_tz)
            timezone_name = profile.timezone

            # Get UTC offset
            utc_offset = local_time.strftime('%z')
            # Format offset as +/-HHMM
            if len(utc_offset) == 5:
                utc_offset = f"{utc_offset[:3]}:{utc_offset[3:]}"
        except:
            # Fallback to UTC if timezone is invalid
            local_time = utc_time
            timezone_name = 'UTC'
            utc_offset = '+00:00'
    else:
        # Default to UTC
        local_time = utc_time
        timezone_name = 'UTC'
        utc_offset = '+00:00'

    return JsonResponse({
        'current_time': local_time.strftime('%Y-%m-%d %H:%M:%S'),
        'date': local_time.strftime('%B %d, %Y'),
        'time': local_time.strftime('%H:%M:%S'),
        'timezone': timezone_name,
        'utc_offset': utc_offset
    })


# Milestone Management Views

@login_required
def milestone_create(request, project_id):
    """Create a new milestone for a project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        form = MilestoneForm(request.POST)
        if form.is_valid():
            milestone = form.save(commit=False)
            milestone.project = project
            milestone.save()
            messages.success(request, f'Milestone "{milestone.title}" added to {project.title}!')
            return redirect('project_detail', project_id=project.id)
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        # Set default order to next available
        next_order = project.milestones.count() + 1
        form = MilestoneForm(initial={'order': next_order})

    # Get available templates
    templates = MilestoneTemplate.objects.filter(is_active=True).order_by('category', 'name')

    return render(request, 'timeline_app/milestone_form.html', {
        'form': form,
        'project': project,
        'title': 'Add Milestone',
        'templates': templates
    })


@login_required
def milestone_edit(request, milestone_id):
    """Edit a milestone"""
    milestone = get_object_or_404(Milestone, id=milestone_id, project__user=request.user)

    if request.method == 'POST':
        form = MilestoneForm(request.POST, instance=milestone)
        if form.is_valid():
            form.save()
            messages.success(request, f'Milestone "{milestone.title}" updated!')
            return redirect('project_detail', project_id=milestone.project.id)
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = MilestoneForm(instance=milestone)

    return render(request, 'timeline_app/milestone_form.html', {
        'form': form,
        'milestone': milestone,
        'project': milestone.project,
        'title': 'Edit Milestone'
    })


@login_required
def milestone_toggle(request, milestone_id):
    """Toggle milestone completion status"""
    milestone = get_object_or_404(Milestone, id=milestone_id, project__user=request.user)

    milestone.is_completed = not milestone.is_completed
    milestone.save()

    if milestone.is_completed:
        # Calculate new progress
        progress = milestone.project.get_progress_percentage()

        # Create celebration message
        if progress >= 100:
            messages.success(request, f'🎉 Congratulations! Project "{milestone.project.title}" is now 100% complete!')
        elif progress >= 80:
            messages.success(request, f'🚀 Amazing! Milestone "{milestone.title}" completed! You\'re {progress:.0f}% done - almost there!')
        elif progress >= 50:
            messages.success(request, f'💪 Great job! Milestone "{milestone.title}" completed! You\'re {progress:.0f}% through your project!')
        else:
            messages.success(request, f'✅ Milestone "{milestone.title}" completed! Keep up the momentum!')

        # Check for streak
        try:
            user_profile = request.user.userprofile
            streak = user_profile.calculate_milestone_streak()
            if streak > 1:
                messages.info(request, f'🔥 You\'re on fire! {streak} day milestone streak!')
        except Exception:
            # If profile doesn't exist or streak calculation fails, just skip
            pass

        # Create celebration notification
        NotificationService.create_celebration_notification(
            user=request.user,
            milestone=milestone,
            project=milestone.project
        )
    else:
        messages.info(request, f'Milestone "{milestone.title}" reopened.')

    return redirect('project_detail', project_id=milestone.project.id)


@login_required
def milestone_delete(request, milestone_id):
    """Delete a milestone"""
    milestone = get_object_or_404(Milestone, id=milestone_id, project__user=request.user)
    project_id = milestone.project.id

    if request.method == 'POST':
        milestone_title = milestone.title
        milestone.delete()
        messages.success(request, f'Milestone "{milestone_title}" deleted!')
        return redirect('project_detail', project_id=project_id)

    return render(request, 'timeline_app/milestone_confirm_delete.html', {
        'milestone': milestone,
        'project': milestone.project
    })


# Dream Milestone Management Views

@login_required
def dream_milestone_create(request, dream_id):
    """Create a new milestone for a dream"""
    dream = get_object_or_404(DreamItem, id=dream_id, user=request.user)

    if request.method == 'POST':
        form = DreamMilestoneForm(request.POST)
        if form.is_valid():
            milestone = form.save(commit=False)
            milestone.dream = dream
            milestone.save()
            messages.success(request, f'Milestone "{milestone.title}" added to {dream.title}!')
            return redirect('dream_detail', dream_id=dream.id)
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        # Set default order to next available
        next_order = dream.milestones.count() + 1
        form = DreamMilestoneForm(initial={'order': next_order})

    return render(request, 'timeline_app/dream_milestone_form.html', {
        'form': form,
        'dream': dream,
        'title': 'Add Milestone'
    })


@login_required
def dream_milestone_toggle(request, milestone_id):
    """Toggle dream milestone completion status"""
    milestone = get_object_or_404(DreamMilestone, id=milestone_id, dream__user=request.user)

    milestone.is_completed = not milestone.is_completed
    milestone.save()

    if milestone.is_completed:
        # Calculate new progress
        progress = milestone.dream.progress_percentage

        # Create celebration message
        if progress >= 100:
            messages.success(request, f'🎉 Congratulations! Dream "{milestone.dream.title}" is now complete!')
        elif progress >= 80:
            messages.success(request, f'🌟 Amazing! Milestone "{milestone.title}" completed! You\'re {progress:.0f}% closer to your dream!')
        elif progress >= 50:
            messages.success(request, f'💫 Great progress! Milestone "{milestone.title}" completed! You\'re {progress:.0f}% there!')
        else:
            messages.success(request, f'✨ Milestone "{milestone.title}" completed! Keep chasing your dreams!')
    else:
        messages.info(request, f'Milestone "{milestone.title}" reopened.')

    return redirect('dream_detail', dream_id=milestone.dream.id)


@login_required
def dream_milestone_delete(request, milestone_id):
    """Delete a dream milestone"""
    milestone = get_object_or_404(DreamMilestone, id=milestone_id, dream__user=request.user)
    dream_id = milestone.dream.id

    if request.method == 'POST':
        milestone_title = milestone.title
        milestone.delete()
        messages.success(request, f'Milestone "{milestone_title}" deleted!')
        return redirect('dream_detail', dream_id=dream_id)

    return render(request, 'timeline_app/dream_milestone_confirm_delete.html', {
        'milestone': milestone,
        'dream': milestone.dream
    })


@login_required
def apply_milestone_template(request, project_id, template_id):
    """Apply a milestone template to a project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)
    template = get_object_or_404(MilestoneTemplate, id=template_id, is_active=True)

    if request.method == 'POST':
        # Clear existing milestones if requested
        if request.POST.get('clear_existing') == 'true':
            project.milestones.all().delete()

        # Create milestones from template
        created_count = 0
        for template_item in template.items.all():
            # Calculate target date based on project creation date
            target_date = None
            if template_item.days_offset and project.created_at:
                target_date = project.created_at.date() + timedelta(days=template_item.days_offset)

            milestone = Milestone.objects.create(
                project=project,
                title=template_item.title,
                description=template_item.description,
                target_date=target_date,
                order=template_item.order
            )
            created_count += 1

        messages.success(request, f'Applied template "{template.name}" - {created_count} milestones created!')
        return redirect('project_detail', project_id=project.id)

    return render(request, 'timeline_app/apply_template.html', {
        'project': project,
        'template': template,
        'existing_milestones': project.milestones.count()
    })


@login_required
def bulk_milestone_action(request, project_id):
    """Perform bulk actions on milestones"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        action = request.POST.get('action')
        milestone_ids = request.POST.getlist('milestone_ids')

        if not milestone_ids:
            messages.error(request, 'No milestones selected.')
            return redirect('project_detail', project_id=project.id)

        milestones = project.milestones.filter(id__in=milestone_ids)

        if action == 'mark_complete':
            updated = milestones.update(is_completed=True)
            messages.success(request, f'Marked {updated} milestone{"s" if updated != 1 else ""} as complete!')

        elif action == 'mark_incomplete':
            updated = milestones.update(is_completed=False)
            messages.success(request, f'Marked {updated} milestone{"s" if updated != 1 else ""} as incomplete!')

        elif action == 'delete':
            count = milestones.count()
            milestones.delete()
            messages.success(request, f'Deleted {count} milestone{"s" if count != 1 else ""}!')

        elif action == 'set_target_date':
            target_date = request.POST.get('target_date')
            if target_date:
                updated = milestones.update(target_date=target_date)
                messages.success(request, f'Updated target date for {updated} milestone{"s" if updated != 1 else ""}!')
            else:
                messages.error(request, 'Please provide a target date.')

    return redirect('project_detail', project_id=project.id)


@login_required
def export_project_milestones(request, project_id):
    """Export project milestones as CSV"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    import csv
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{project.title}_milestones.csv"'

    writer = csv.writer(response)
    writer.writerow(['Order', 'Title', 'Description', 'Target Date', 'Status', 'Completed Date'])

    for milestone in project.milestones.all():
        writer.writerow([
            milestone.order,
            milestone.title,
            milestone.description,
            milestone.target_date.strftime('%Y-%m-%d') if milestone.target_date else '',
            'Completed' if milestone.is_completed else 'Pending',
            milestone.completed_date.strftime('%Y-%m-%d %H:%M') if milestone.completed_date else ''
        ])

    return response


@login_required
def analytics_dashboard(request):
    """Advanced analytics dashboard"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    # Get all user data
    projects = Project.objects.filter(user=request.user)
    dreams = DreamItem.objects.filter(user=request.user)
    milestones = Milestone.objects.filter(project__user=request.user)
    dream_milestones = DreamMilestone.objects.filter(dream__user=request.user)

    # Calculate advanced metrics
    from django.db.models import Count, Avg, Q
    from datetime import datetime, timedelta

    # Time-based analytics
    now = timezone.now()
    last_30_days = now - timedelta(days=30)
    last_7_days = now - timedelta(days=7)

    # Project analytics
    project_analytics = {
        'total_projects': projects.count(),
        'completed_projects': projects.filter(status='completed').count(),
        'active_projects': projects.filter(status__in=['not_started', 'in_progress']).count(),
        'projects_this_month': projects.filter(created_at__gte=last_30_days).count(),
        'avg_project_duration': calculate_avg_project_duration(projects),
        'completion_rate_trend': calculate_completion_trend(projects),
    }

    # Milestone analytics
    milestone_analytics = {
        'total_milestones': milestones.count() + dream_milestones.count(),
        'completed_milestones': milestones.filter(is_completed=True).count() + dream_milestones.filter(is_completed=True).count(),
        'overdue_milestones': milestones.filter(target_date__lt=now.date(), is_completed=False).count(),
        'milestones_this_week': milestones.filter(completed_date__gte=last_7_days).count() + dream_milestones.filter(completed_date__gte=last_7_days).count(),
        'avg_completion_time': calculate_avg_completion_time(milestones, dream_milestones),
        'productivity_score': calculate_productivity_score(request.user),
    }

    # Time analytics
    time_analytics = {
        'most_productive_day': get_most_productive_day(request.user),
        'most_productive_hour': get_most_productive_hour(request.user),
        'streak_data': get_detailed_streak_data(request.user),
        'monthly_progress': get_monthly_progress_data(request.user),
    }

    # Category analytics
    category_analytics = get_category_breakdown(projects, dreams)

    # Goal achievement insights
    achievement_insights = {
        'dream_completion_rate': dreams.filter(is_achieved=True).count() / max(dreams.count(), 1) * 100,
        'average_milestones_per_project': milestones.count() / max(projects.count(), 1),
        'success_patterns': identify_success_patterns(request.user),
        'improvement_suggestions': generate_improvement_suggestions(request.user),
    }

    context = {
        'profile': profile,
        'project_analytics': project_analytics,
        'milestone_analytics': milestone_analytics,
        'time_analytics': time_analytics,
        'category_analytics': category_analytics,
        'achievement_insights': achievement_insights,
    }

    return render(request, 'timeline_app/analytics_dashboard.html', context)


def calculate_avg_project_duration(projects):
    """Calculate average project duration in days"""
    completed_projects = projects.filter(status='completed')
    if not completed_projects:
        return 0

    total_days = 0
    count = 0
    for project in completed_projects:
        if project.created_at and project.updated_at:
            duration = (project.updated_at.date() - project.created_at.date()).days
            if duration > 0:  # Only count projects that took at least 1 day
                total_days += duration
                count += 1

    return total_days / count if count > 0 else 0


def calculate_completion_trend(projects):
    """Calculate completion trend over last 6 months"""
    from datetime import datetime, timedelta
    import calendar

    now = timezone.now()
    trend_data = []

    for i in range(6):
        month_start = (now - timedelta(days=30*i)).replace(day=1)
        month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])

        completed_in_month = projects.filter(
            status='completed',
            updated_at__gte=month_start,
            updated_at__lte=month_end
        ).count()

        trend_data.append({
            'month': month_start.strftime('%b %Y'),
            'completed': completed_in_month
        })

    return list(reversed(trend_data))


def calculate_avg_completion_time(milestones, dream_milestones):
    """Calculate average time to complete milestones"""
    all_completed = []

    for milestone in milestones.filter(is_completed=True, completed_date__isnull=False):
        if milestone.target_date and milestone.completed_date:
            days_diff = (milestone.completed_date.date() - milestone.target_date).days
            all_completed.append(days_diff)

    for milestone in dream_milestones.filter(is_completed=True, completed_date__isnull=False):
        if milestone.target_date and milestone.completed_date:
            days_diff = (milestone.completed_date.date() - milestone.target_date).days
            all_completed.append(days_diff)

    return sum(all_completed) / len(all_completed) if all_completed else 0


def calculate_productivity_score(user):
    """Calculate user productivity score (0-100)"""
    from datetime import timedelta

    now = timezone.now()
    last_30_days = now - timedelta(days=30)

    # Get recent activity
    recent_milestones = Milestone.objects.filter(
        project__user=user,
        completed_date__gte=last_30_days,
        is_completed=True
    ).count()

    recent_dream_milestones = DreamMilestone.objects.filter(
        dream__user=user,
        completed_date__gte=last_30_days,
        is_completed=True
    ).count()

    total_recent = recent_milestones + recent_dream_milestones

    # Calculate score based on activity (max 30 milestones in 30 days = 100 score)
    score = min(100, (total_recent / 30) * 100)
    return round(score, 1)


def get_most_productive_day(user):
    """Find user's most productive day of the week"""
    from django.db.models import Count

    # Get all completed milestones with completion dates
    milestones = Milestone.objects.filter(
        project__user=user,
        is_completed=True,
        completed_date__isnull=False
    )

    dream_milestones = DreamMilestone.objects.filter(
        dream__user=user,
        is_completed=True,
        completed_date__isnull=False
    )

    # Count completions by day of week
    day_counts = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}  # Monday = 0

    for milestone in milestones:
        day_of_week = milestone.completed_date.weekday()
        day_counts[day_of_week] += 1

    for milestone in dream_milestones:
        day_of_week = milestone.completed_date.weekday()
        day_counts[day_of_week] += 1

    if not any(day_counts.values()):
        return 'Monday'  # Default

    most_productive_day_num = max(day_counts, key=day_counts.get)
    days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    return days[most_productive_day_num]


def get_most_productive_hour(user):
    """Find user's most productive hour of the day"""
    milestones = Milestone.objects.filter(
        project__user=user,
        is_completed=True,
        completed_date__isnull=False
    )

    dream_milestones = DreamMilestone.objects.filter(
        dream__user=user,
        is_completed=True,
        completed_date__isnull=False
    )

    # Count completions by hour
    hour_counts = {i: 0 for i in range(24)}

    for milestone in milestones:
        hour = milestone.completed_date.hour
        hour_counts[hour] += 1

    for milestone in dream_milestones:
        hour = milestone.completed_date.hour
        hour_counts[hour] += 1

    if not any(hour_counts.values()):
        return '9:00 AM'  # Default

    most_productive_hour = max(hour_counts, key=hour_counts.get)

    # Convert to 12-hour format
    if most_productive_hour == 0:
        return '12:00 AM'
    elif most_productive_hour < 12:
        return f'{most_productive_hour}:00 AM'
    elif most_productive_hour == 12:
        return '12:00 PM'
    else:
        return f'{most_productive_hour - 12}:00 PM'


def get_detailed_streak_data(user):
    """Get detailed streak information"""
    try:
        profile = user.userprofile
        current_streak = profile.calculate_milestone_streak()

        # Calculate longest streak
        longest_streak = calculate_longest_streak(user)

        return {
            'current_streak': current_streak,
            'longest_streak': longest_streak,
            'streak_percentage': min(100, (current_streak / 30) * 100)  # 30 days = 100%
        }
    except:
        return {
            'current_streak': 0,
            'longest_streak': 0,
            'streak_percentage': 0
        }


def calculate_longest_streak(user):
    """Calculate user's longest milestone completion streak"""
    from datetime import timedelta

    # Get all completed milestones ordered by completion date
    all_milestones = []

    milestones = Milestone.objects.filter(
        project__user=user,
        is_completed=True,
        completed_date__isnull=False
    ).order_by('completed_date')

    dream_milestones = DreamMilestone.objects.filter(
        dream__user=user,
        is_completed=True,
        completed_date__isnull=False
    ).order_by('completed_date')

    # Combine and sort by date
    for milestone in milestones:
        all_milestones.append(milestone.completed_date.date())

    for milestone in dream_milestones:
        all_milestones.append(milestone.completed_date.date())

    all_milestones.sort()

    if not all_milestones:
        return 0

    # Calculate longest consecutive streak
    longest_streak = 1
    current_streak = 1

    for i in range(1, len(all_milestones)):
        days_diff = (all_milestones[i] - all_milestones[i-1]).days

        if days_diff <= 1:  # Same day or next day
            current_streak += 1
            longest_streak = max(longest_streak, current_streak)
        else:
            current_streak = 1

    return longest_streak


def get_monthly_progress_data(user):
    """Get monthly progress data for charts"""
    from datetime import datetime, timedelta
    import calendar

    now = timezone.now()
    monthly_data = []

    for i in range(12):
        month_start = (now - timedelta(days=30*i)).replace(day=1)
        month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])

        # Count milestones completed in this month
        milestones_completed = Milestone.objects.filter(
            project__user=user,
            is_completed=True,
            completed_date__gte=month_start,
            completed_date__lte=month_end
        ).count()

        dream_milestones_completed = DreamMilestone.objects.filter(
            dream__user=user,
            is_completed=True,
            completed_date__gte=month_start,
            completed_date__lte=month_end
        ).count()

        total_completed = milestones_completed + dream_milestones_completed

        monthly_data.append({
            'month': month_start.strftime('%b'),
            'year': month_start.year,
            'completed': total_completed
        })

    return list(reversed(monthly_data))


def get_category_breakdown(projects, dreams):
    """Get breakdown of projects and dreams by category"""
    from django.db.models import Count

    # Project categories (based on status and type)
    project_breakdown = {
        'active': projects.filter(status__in=['not_started', 'in_progress']).count(),
        'completed': projects.filter(status='completed').count(),
        'on_hold': projects.filter(status='on_hold').count(),
    }

    # Dream categories (based on priority)
    dream_breakdown = {
        'high_priority': dreams.filter(priority__gte=8).count(),
        'medium_priority': dreams.filter(priority__gte=5, priority__lt=8).count(),
        'low_priority': dreams.filter(priority__lt=5).count(),
        'achieved': dreams.filter(is_achieved=True).count(),
    }

    return {
        'projects': project_breakdown,
        'dreams': dream_breakdown
    }


def identify_success_patterns(user):
    """Identify patterns in user's successful projects"""
    completed_projects = Project.objects.filter(user=user, status='completed')

    if not completed_projects:
        return []

    patterns = []

    # Check if user completes projects faster when they have milestones
    projects_with_milestones = completed_projects.filter(milestones__isnull=False).distinct()
    projects_without_milestones = completed_projects.exclude(milestones__isnull=False)

    if projects_with_milestones.count() > projects_without_milestones.count():
        patterns.append("You complete projects 60% faster when you break them into milestones!")

    # Check completion day patterns
    most_productive_day = get_most_productive_day(user)
    patterns.append(f"You're most productive on {most_productive_day}s - consider scheduling important tasks then!")

    # Check project duration patterns
    avg_duration = calculate_avg_project_duration(completed_projects)
    if avg_duration > 0:
        if avg_duration < 30:
            patterns.append("You excel at short-term projects! Consider breaking larger goals into 30-day sprints.")
        elif avg_duration > 90:
            patterns.append("You have great persistence with long-term projects! Your dedication pays off.")

    return patterns


def generate_improvement_suggestions(user):
    """Generate personalized improvement suggestions"""
    suggestions = []

    # Check recent activity
    from datetime import timedelta
    now = timezone.now()
    last_week = now - timedelta(days=7)

    recent_activity = Milestone.objects.filter(
        project__user=user,
        completed_date__gte=last_week,
        is_completed=True
    ).count()

    if recent_activity == 0:
        suggestions.append("Try completing at least one milestone this week to build momentum!")
    elif recent_activity < 3:
        suggestions.append("Great start! Try to complete 3-5 milestones per week for optimal progress.")
    else:
        suggestions.append("Excellent momentum! You're on track for achieving your goals.")

    # Check overdue milestones
    overdue_count = Milestone.objects.filter(
        project__user=user,
        target_date__lt=now.date(),
        is_completed=False
    ).count()

    if overdue_count > 0:
        suggestions.append(f"You have {overdue_count} overdue milestone{'s' if overdue_count != 1 else ''}. Consider reviewing and updating target dates.")

    # Check project without milestones
    projects_without_milestones = Project.objects.filter(
        user=user,
        status__in=['not_started', 'in_progress'],
        milestones__isnull=True
    ).count()

    if projects_without_milestones > 0:
        suggestions.append("Add milestones to your projects for better tracking and 60% faster completion!")

    return suggestions


@login_required
def notifications_view(request):
    """View all notifications for the user"""
    notifications = Notification.objects.for_user(request.user).active().order_by('-created_at')

    # Mark all as read when viewing
    notifications.filter(is_read=False).update(is_read=True)

    context = {
        'notifications': notifications,
        'unread_count': 0,  # All marked as read now
    }

    return render(request, 'timeline_app/notifications.html', context)


@login_required
def dismiss_notification(request, notification_id):
    """Dismiss a specific notification"""
    notification = get_object_or_404(Notification, id=notification_id, user=request.user)
    notification.is_dismissed = True
    notification.save()

    return JsonResponse({'status': 'success'})


@login_required
def get_notification_count(request):
    """API endpoint to get unread notification count"""
    count = Notification.objects.for_user(request.user).active().filter(is_read=False).count()

    return JsonResponse({'count': count})
