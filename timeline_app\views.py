from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from datetime import date, datetime
from .models import UserProfile, Project
from .forms import UserProfileForm, ProjectForm, CustomUserCreationForm


def landing_page(request):
    """Landing page with login/register options"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    return render(request, 'timeline_app/landing.html')


def register_view(request):
    """User registration view"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Create user profile
            UserProfile.objects.create(user=user)
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}!')
            return redirect('login')
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})


@login_required
def dashboard(request):
    """Main dashboard after login"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    projects = Project.objects.filter(user=request.user)

    context = {
        'profile': profile,
        'projects': projects,
        'current_time': timezone.now(),
    }
    return render(request, 'timeline_app/dashboard.html', context)


@login_required
def profile_edit(request):
    """Edit user profile"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=profile, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('dashboard')
    else:
        form = UserProfileForm(instance=profile, user=request.user)

    return render(request, 'timeline_app/profile_edit.html', {'form': form, 'profile': profile})


@login_required
def weeks_view(request):
    """Detailed weeks view of life timeline"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if not profile.birth_date:
        messages.warning(request, 'Please set your birth date in your profile first.')
        return redirect('profile_edit')

    # Calculate weeks data
    weeks_data = []
    if profile.birth_date:
        total_weeks = int(100 * 52.18)  # Approximate weeks in 100 years
        weeks_lived = profile.weeks_lived

        # Create grid of weeks (52 weeks per year, 100 years)
        for year in range(100):
            year_weeks = []
            for week in range(52):
                week_number = year * 52 + week
                is_lived = week_number < weeks_lived
                year_weeks.append({
                    'week': week + 1,
                    'is_lived': is_lived,
                    'year': year + 1
                })
            weeks_data.append({
                'year': year + 1,
                'weeks': year_weeks
            })

    context = {
        'profile': profile,
        'weeks_data': weeks_data,
        'total_weeks': int(100 * 52.18),
        'weeks_lived': profile.weeks_lived,
        'weeks_remaining': profile.weeks_remaining,
    }
    return render(request, 'timeline_app/weeks_view.html', context)


@login_required
def project_create(request):
    """Create a new project"""
    if request.method == 'POST':
        form = ProjectForm(request.POST)
        if form.is_valid():
            project = form.save(commit=False)
            project.user = request.user
            project.save()
            messages.success(request, 'Project created successfully!')
            return redirect('dashboard')
    else:
        form = ProjectForm()

    return render(request, 'timeline_app/project_form.html', {'form': form, 'title': 'Create Project'})


@login_required
def project_edit(request, project_id):
    """Edit an existing project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        form = ProjectForm(request.POST, instance=project)
        if form.is_valid():
            form.save()
            messages.success(request, 'Project updated successfully!')
            return redirect('dashboard')
    else:
        form = ProjectForm(instance=project)

    return render(request, 'timeline_app/project_form.html', {'form': form, 'title': 'Edit Project', 'project': project})


@login_required
def project_delete(request, project_id):
    """Delete a project"""
    project = get_object_or_404(Project, id=project_id, user=request.user)

    if request.method == 'POST':
        project.delete()
        messages.success(request, 'Project deleted successfully!')
        return redirect('dashboard')

    return render(request, 'timeline_app/project_confirm_delete.html', {'project': project})


@login_required
def get_current_time(request):
    """API endpoint to get current time"""
    from django.utils import timezone as django_timezone

    # Get user's timezone preference
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    # Get current time (for now in UTC, can be enhanced with pytz later)
    current_time = django_timezone.now()

    return JsonResponse({
        'current_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
        'date': current_time.strftime('%B %d, %Y'),
        'time': current_time.strftime('%H:%M:%S'),
        'timezone': profile.timezone,
        'utc_offset': '+0000'  # UTC for now
    })
