from django import template
import random

register = template.Library()

@register.filter
def random_tile_size(value, index):
    """Generate a random tile size based on priority and index"""
    priority = int(value) if value else 5
    
    # Seed random with priority and index for consistency
    random.seed(priority + index)
    
    # Higher priority dreams have higher chance of being large
    if priority >= 9:
        sizes = ['tile-size-extra-large', 'tile-size-large', 'tile-size-large']
    elif priority >= 7:
        sizes = ['tile-size-large', 'tile-size-medium', 'tile-size-large']
    elif priority >= 5:
        sizes = ['tile-size-medium', 'tile-size-small', 'tile-size-medium']
    else:
        sizes = ['tile-size-small', 'tile-size-small', 'tile-size-medium']
    
    return random.choice(sizes)

@register.filter
def priority_stars(priority):
    """Generate star display based on priority (1-10 scale)"""
    priority = int(priority) if priority else 0
    
    if priority >= 9:
        return 5  # 5 stars for priority 9-10
    elif priority >= 7:
        return 4  # 4 stars for priority 7-8
    elif priority >= 5:
        return 3  # 3 stars for priority 5-6
    elif priority >= 3:
        return 2  # 2 stars for priority 3-4
    else:
        return 1  # 1 star for priority 1-2

@register.filter
def multiply(value, arg):
    """Multiply filter for calculations"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0
