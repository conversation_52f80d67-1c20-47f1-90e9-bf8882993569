{% extends 'base.html' %}

{% block title %}{{ title }} - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-flag-checkered"></i> {{ title }}
                    </h4>
                    <small>Project: {{ project.title }}</small>
                </div>
                
                <div class="card-body">
                    <!-- Template Selection -->
                    {% if templates %}
                    <div class="template-selection mb-4">
                        <h6><i class="fas fa-magic"></i> Quick Start with Templates</h6>
                        <p class="text-muted">Choose a template to quickly set up milestones for your project:</p>
                        <div class="row">
                            {% for template in templates %}
                                <div class="col-md-6 mb-2">
                                    <div class="template-card" onclick="selectTemplate({{ template.id }})">
                                        <div class="template-info">
                                            <strong>{{ template.name }}</strong>
                                            <br><small class="text-muted">{{ template.get_category_display }} • {{ template.items.count }} milestones</small>
                                        </div>
                                        <div class="template-action">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <hr class="my-4">
                        <h6><i class="fas fa-plus"></i> Or Create Individual Milestone</h6>
                    </div>
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.title.id_for_label }}" class="form-label">
                                    {{ form.title.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.title.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.order.id_for_label }}" class="form-label">
                                    {{ form.order.label }}
                                </label>
                                {{ form.order }}
                                {% if form.order.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.order.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">{{ form.order.help_text }}</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.target_date.id_for_label }}" class="form-label">
                                {{ form.target_date.label }}
                            </label>
                            {{ form.target_date }}
                            {% if form.target_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.target_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.target_date.help_text }}</small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'project_detail' project.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Project
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Milestone
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.template-selection {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.template-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-card:hover {
    border-color: #007bff;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.template-info strong {
    color: #2c3e50;
}

.template-action {
    color: #007bff;
    font-size: 1.1rem;
}

/* Dark theme support */
[data-theme="dark"] .template-selection {
    background: #343a40;
    border-color: #495057;
}

[data-theme="dark"] .template-card {
    background: #212529;
    border-color: #495057;
}

[data-theme="dark"] .template-card:hover {
    background: #2c3e50;
    border-color: #4c6ef5;
}

[data-theme="dark"] .template-info strong {
    color: #f8f9fa;
}
</style>

<script>
function selectTemplate(templateId) {
    if (confirm('This will take you to the template application page. Continue?')) {
        window.location.href = `/projects/{{ project.id }}/templates/${templateId}/apply/`;
    }
}
</script>
{% endblock %}
