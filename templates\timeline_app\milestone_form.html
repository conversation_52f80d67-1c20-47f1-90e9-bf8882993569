{% extends 'base.html' %}

{% block title %}{{ title }} - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-flag-checkered"></i> {{ title }}
                    </h4>
                    <small>Project: {{ project.title }}</small>
                </div>
                
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.title.id_for_label }}" class="form-label">
                                    {{ form.title.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.title.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.order.id_for_label }}" class="form-label">
                                    {{ form.order.label }}
                                </label>
                                {{ form.order }}
                                {% if form.order.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.order.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">{{ form.order.help_text }}</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.target_date.id_for_label }}" class="form-label">
                                {{ form.target_date.label }}
                            </label>
                            {{ form.target_date }}
                            {% if form.target_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.target_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.target_date.help_text }}</small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'project_detail' project.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Project
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Milestone
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
