#!/usr/bin/env python
import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'timeline_project.settings')
django.setup()

from timeline_app.models import ProjectCategory, ProjectTag, DreamItem
from django.contrib.auth.models import User
from datetime import date, timedelta
import random

def create_categories():
    """Create default project categories"""
    categories = [
        {'name': 'Personal Development', 'color': '#6f42c1', 'icon': 'fas fa-user-graduate'},
        {'name': 'Health & Fitness', 'color': '#28a745', 'icon': 'fas fa-heartbeat'},
        {'name': 'Career & Business', 'color': '#007bff', 'icon': 'fas fa-briefcase'},
        {'name': 'Relationships', 'color': '#e83e8c', 'icon': 'fas fa-heart'},
        {'name': 'Travel & Adventure', 'color': '#17a2b8', 'icon': 'fas fa-plane'},
        {'name': 'Learning & Education', 'color': '#fd7e14', 'icon': 'fas fa-book'},
        {'name': 'Creative Projects', 'color': '#20c997', 'icon': 'fas fa-palette'},
        {'name': 'Home & Lifestyle', 'color': '#ffc107', 'icon': 'fas fa-home'},
        {'name': 'Financial Goals', 'color': '#dc3545', 'icon': 'fas fa-dollar-sign'},
        {'name': 'Hobbies & Fun', 'color': '#6c757d', 'icon': 'fas fa-gamepad'},
    ]
    
    created_count = 0
    for cat_data in categories:
        category, created = ProjectCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults={
                'color': cat_data['color'],
                'icon': cat_data['icon'],
                'description': f"Projects related to {cat_data['name'].lower()}"
            }
        )
        if created:
            created_count += 1
            print(f"✅ Created category: {category.name}")
    
    print(f"📁 Categories: {created_count} created, {ProjectCategory.objects.count()} total")

def create_tags():
    """Create default project tags"""
    tags = [
        {'name': 'urgent', 'color': '#dc3545'},
        {'name': 'long-term', 'color': '#6f42c1'},
        {'name': 'daily-habit', 'color': '#28a745'},
        {'name': 'skill-building', 'color': '#007bff'},
        {'name': 'creative', 'color': '#20c997'},
        {'name': 'social', 'color': '#e83e8c'},
        {'name': 'physical', 'color': '#fd7e14'},
        {'name': 'mental', 'color': '#17a2b8'},
        {'name': 'financial', 'color': '#ffc107'},
        {'name': 'family', 'color': '#6c757d'},
        {'name': 'work', 'color': '#495057'},
        {'name': 'fun', 'color': '#f8f9fa'},
    ]
    
    created_count = 0
    for tag_data in tags:
        tag, created = ProjectTag.objects.get_or_create(
            name=tag_data['name'],
            defaults={'color': tag_data['color']}
        )
        if created:
            created_count += 1
            print(f"🏷️ Created tag: {tag.name}")
    
    print(f"🏷️ Tags: {created_count} created, {ProjectTag.objects.count()} total")

def create_sample_dreams():
    """Create sample dreams for demo purposes"""
    # Only create if there's a demo user
    try:
        demo_user = User.objects.get(username='demo')
    except User.DoesNotExist:
        print("ℹ️ No demo user found, skipping sample dreams")
        return
    
    sample_dreams = [
        {
            'title': 'Travel to Japan',
            'description': 'Experience the culture, food, and beautiful landscapes of Japan',
            'category': 'travel',
            'priority': 8,
            'image_url': 'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=400',
        },
        {
            'title': 'Learn to Play Piano',
            'description': 'Master the piano and play my favorite songs',
            'category': 'education',
            'priority': 6,
            'image_url': 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?w=400',
        },
        {
            'title': 'Start My Own Business',
            'description': 'Launch a successful business in tech or creative field',
            'category': 'career',
            'priority': 9,
            'image_url': 'https://images.unsplash.com/photo-**********-b413da4baf72?w=400',
        },
        {
            'title': 'Run a Marathon',
            'description': 'Complete a full 26.2 mile marathon race',
            'category': 'health',
            'priority': 7,
            'image_url': 'https://images.unsplash.com/photo-**********-fa95b6ee9643?w=400',
        },
        {
            'title': 'Buy My Dream House',
            'description': 'Own a beautiful home with a garden and modern amenities',
            'category': 'lifestyle',
            'priority': 8,
            'image_url': 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400',
        },
        {
            'title': 'Write a Novel',
            'description': 'Complete and publish my first novel',
            'category': 'experiences',
            'priority': 5,
            'image_url': 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=400',
        },
    ]
    
    created_count = 0
    for dream_data in sample_dreams:
        # Check if dream already exists
        if not DreamItem.objects.filter(user=demo_user, title=dream_data['title']).exists():
            dream = DreamItem.objects.create(
                user=demo_user,
                title=dream_data['title'],
                description=dream_data['description'],
                category=dream_data['category'],
                priority=dream_data['priority'],
                image_url=dream_data['image_url'],
                target_date=date.today() + timedelta(days=random.randint(365, 1825))  # 1-5 years
            )
            created_count += 1
            print(f"⭐ Created dream: {dream.title}")
    
    print(f"⭐ Sample dreams: {created_count} created for demo user")

def main():
    print("🚀 Creating initial data for TimeLine application...")
    print("=" * 50)
    
    create_categories()
    print()
    create_tags()
    print()
    create_sample_dreams()
    
    print("=" * 50)
    print("✅ Initial data creation completed!")
    print("\n📊 Summary:")
    print(f"   📁 Categories: {ProjectCategory.objects.count()}")
    print(f"   🏷️ Tags: {ProjectTag.objects.count()}")
    print(f"   ⭐ Dreams: {DreamItem.objects.count()}")
    print(f"   👥 Users: {User.objects.count()}")

if __name__ == '__main__':
    main()
