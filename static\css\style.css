/* Custom styles for TimeLine application */

/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --bg-primary: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    --bg-secondary: #ffffff;
    --bg-tertiary: #f8f9fa;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #e9ecef;
    --shadow-light: rgba(0,0,0,0.1);
    --shadow-medium: rgba(0,0,0,0.15);
    --shadow-heavy: rgba(0,0,0,0.2);
    --accent-primary: #667eea;
    --accent-secondary: #764ba2;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #007bff;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --bg-secondary: #2c2c54;
    --bg-tertiary: #40407a;
    --text-primary: #f8f9fa;
    --text-secondary: #adb5bd;
    --text-muted: #6c757d;
    --border-color: #495057;
    --shadow-light: rgba(0,0,0,0.3);
    --shadow-medium: rgba(0,0,0,0.4);
    --shadow-heavy: rgba(0,0,0,0.5);
    --accent-primary: #8b9dc3;
    --accent-secondary: #a29bfe;
    --success: #00b894;
    --warning: #fdcb6e;
    --danger: #e84393;
    --info: #74b9ff;
}

/* General styles */
body {
    background: var(--bg-primary);
    min-height: 100vh;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.gradient-text {
    background: linear-gradient(45deg, #ffd700, #ffed4e, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.4rem;
    font-weight: 300;
    opacity: 0.95;
}

.hero-description {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto;
}

/* Demo Timeline in Hero */
.demo-timeline-container {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.demo-progress-bar {
    position: relative;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 20px;
    overflow: hidden;
    margin: 1rem 0;
}

.demo-progress-fill {
    height: 100%;
    width: 28.5%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #ffc107 100%);
    border-radius: 20px;
    position: relative;
    animation: fillAnimation 2s ease-in-out;
}

.demo-sand-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.4) 1px, transparent 1px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.4) 1px, transparent 1px);
    background-size: 15px 15px, 20px 20px;
    animation: sandFlow 4s linear infinite;
}

.demo-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: #333;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

.demo-markers {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.5rem;
}

@keyframes fillAnimation {
    0% { width: 0%; }
    100% { width: 28.5%; }
}

@keyframes sandFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Button Styles */
.btn-hero {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-hero:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
    text-align: center;
    margin-bottom: 1.5rem;
}

.feature-demo {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

/* Mini Timeline Demo */
.mini-timeline {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.mini-progress {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    animation: miniProgress 2s ease-in-out;
}

@keyframes miniProgress {
    0% { width: 0%; }
    100% { width: 35%; }
}

/* Weeks Preview */
.weeks-preview {
    display: flex;
    justify-content: center;
    gap: 3px;
    margin-bottom: 0.5rem;
}

.week-square {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.week-square.lived {
    background-color: #28a745;
    animation: weekAppear 0.5s ease-in-out;
}

.week-square.future {
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
}

@keyframes weekAppear {
    0% { transform: scale(0); }
    100% { transform: scale(1); }
}

/* Project Preview */
.project-preview {
    margin-bottom: 0.5rem;
}

.project-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.project-name {
    font-size: 0.9rem;
    font-weight: 500;
    min-width: 80px;
}

.project-progress {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.project-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    animation: projectProgress 2s ease-in-out;
}

@keyframes projectProgress {
    0% { width: 0%; }
    100% { width: 60%; }
}

/* How It Works Section */
.how-it-works-section {
    background: white;
    border-radius: 20px;
    padding: 3rem 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.step-card {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 2rem;
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.step-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.step-number-large {
    position: absolute;
    top: -15px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.step-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #28a745, #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.step-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.step-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.step-visual {
    padding: 1rem;
    background: white;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.calendar-icon i {
    font-size: 2rem;
}

.project-icons i {
    font-size: 1.5rem;
}

.progress-demo {
    width: 100%;
}

.demo-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.demo-fill {
    height: 100%;
    width: 75%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 4px;
    animation: demoFill 2s ease-in-out;
}

@keyframes demoFill {
    0% { width: 0%; }
    100% { width: 75%; }
}

/* Benefits Section */
.benefits-section {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 20px;
    padding: 3rem 2rem;
    color: white;
}

.benefit-card {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    height: 100%;
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.benefit-card h5 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefit-card p {
    opacity: 0.9;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.benefit-stats {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #ffd700;
}

.stat-text {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Call to Action Section */
.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 4rem 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="60" cy="30" r="1" fill="rgba(255,255,255,0.05)"/></svg>');
    opacity: 0.3;
}

.cta-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.cta-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.cta-buttons {
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.btn-cta {
    padding: 15px 40px;
    font-weight: 600;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

.btn-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.feature-list {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature-item {
    font-size: 1rem;
    opacity: 0.9;
}

/* Legacy step styles (keeping for compatibility) */
.step-item {
    padding: 1rem;
}

.step-number {
    width: 50px;
    height: 50px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin: 0 auto 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .feature-list {
        flex-direction: column;
        gap: 1rem;
    }

    .demo-timeline-container {
        padding: 1rem;
    }

    .step-card {
        margin-bottom: 2rem;
    }

    .btn-hero, .btn-cta {
        padding: 10px 25px;
        font-size: 0.9rem;
    }

    .hero-section {
        border-radius: 15px;
    }

    .feature-card, .step-card, .benefit-card {
        border-radius: 15px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .cta-title {
        font-size: 1.8rem;
    }

    .demo-markers {
        font-size: 0.8rem;
    }

    .weeks-preview {
        gap: 2px;
    }

    .week-square {
        width: 10px;
        height: 10px;
    }
}

/* Profile Settings Styles */
.profile-header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.profile-header-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

.profile-header-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.profile-avatar {
    font-size: 4rem;
    color: rgba(255,255,255,0.9);
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.profile-username {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.life-stage-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.profile-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.stat-label {
    font-size: 0.9rem;
    color: #5a6c7d;
    font-weight: 500;
    margin-top: 0.25rem;
}

/* Settings Container */
.settings-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.settings-form {
    padding: 0;
}

.settings-section {
    padding: 2rem;
    border-bottom: 1px solid #f0f0f0;
}

.settings-section:last-child {
    border-bottom: none;
}

.section-header {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-description {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-control.is-valid {
    border-color: #28a745;
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.form-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Checkbox Styling */
.form-check-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-check-container:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.form-check-input {
    width: 1.2rem;
    height: 1.2rem;
    margin-right: 0.75rem;
}

.form-check-label {
    font-weight: 500;
    color: #2c3e50;
    cursor: pointer;
    display: flex;
    align-items: center;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Form Actions */
.form-actions {
    padding: 2rem;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

/* Timeline Preview Card */
.timeline-preview-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.timeline-preview-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1.5rem 2rem;
}

.timeline-preview-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.timeline-preview-subtitle {
    opacity: 0.9;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.timeline-preview-body {
    padding: 2rem;
}

.timeline-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Timezone Preview Styles */
.timezone-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.timezone-preview:hover {
    background: #e9ecef;
    border-color: #667eea;
}

#timezone-preview {
    font-weight: 500;
    color: #2c3e50;
}

/* Enhanced Time Display */
#current-time {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

#current-time:hover {
    background: rgba(255,255,255,0.15);
    transform: translateY(-1px);
}

#timezone-info {
    font-size: 0.8rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Form Enhancement */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-group {
    position: relative;
}

.form-group .form-label {
    transition: all 0.3s ease;
}

.form-group:focus-within .form-label {
    color: #667eea;
    transform: translateY(-2px);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error States */
.form-success {
    border-color: #28a745 !important;
    background-color: rgba(40, 167, 69, 0.1);
}

.form-error-state {
    border-color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.1);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
}

.notification.info {
    background: linear-gradient(135deg, #007bff, #6f42c1);
}

/* Analytics Cards */
.analytics-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px var(--shadow-light);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.analytics-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.analytics-content {
    flex: 1;
}

.analytics-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.analytics-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Deadline and Activity Items */
.deadline-item, .activity-item {
    padding: 0.75rem 0;
    transition: all 0.3s ease;
}

.deadline-item:hover, .activity-item:hover {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
    margin: 0 -0.75rem;
}

/* Progress Circle */
.progress-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: conic-gradient(
        #28a745 0deg,
        #28a745 calc(var(--progress, 0) * 3.6deg),
        #e9ecef calc(var(--progress, 0) * 3.6deg),
        #e9ecef 360deg
    );
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 0.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.progress-circle::before {
    content: '';
    position: absolute;
    width: 35px;
    height: 35px;
    background: white;
    border-radius: 50%;
    z-index: 1;
}

.progress-circle span {
    position: relative;
    z-index: 2;
}

/* Enhanced Project Cards */
.project-card {
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.project-card:hover::before {
    transform: translateX(100%);
}

.project-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-left-color: #28a745;
}

/* Priority Badge Colors */
.badge.bg-low { background-color: #6c757d !important; }
.badge.bg-medium { background-color: #ffc107 !important; color: #000 !important; }
.badge.bg-high { background-color: #fd7e14 !important; }
.badge.bg-urgent { background-color: #dc3545 !important; }

/* Status Badge Colors */
.badge.bg-not_started { background-color: #6c757d !important; }
.badge.bg-in_progress { background-color: #007bff !important; }
.badge.bg-completed { background-color: #28a745 !important; }
.badge.bg-on_hold { background-color: #ffc107 !important; color: #000 !important; }
.badge.bg-cancelled { background-color: #dc3545 !important; }

/* Animated Counters */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.analytics-number {
    animation: countUp 0.6s ease-out;
}

/* Responsive Analytics */
@media (max-width: 768px) {
    .analytics-card {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .analytics-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .analytics-number {
        font-size: 1.5rem;
    }

    .progress-circle {
        width: 40px;
        height: 40px;
        font-size: 0.7rem;
    }

    .progress-circle::before {
        width: 28px;
        height: 28px;
    }
}

/* Achievement Cards */
.achievement-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px var(--shadow-light);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.achievement-content {
    flex: 1;
}

.achievement-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.achievement-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Completed Projects */
.completed-project-card {
    background: var(--bg-secondary);
    border-radius: 10px;
    padding: 1rem;
    border-left: 4px solid var(--success);
    transition: all 0.3s ease;
    height: 100%;
}

.completed-project-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px var(--shadow-medium);
}

.project-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.project-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0;
    flex: 1;
}

.completion-date {
    font-size: 0.8rem;
    color: var(--success);
    font-weight: 500;
}

.project-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
}

.year-header {
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

/* Life Timeline */
.timeline-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-item {
    display: flex;
    margin-bottom: 3rem;
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.timeline-item.animate-in {
    opacity: 1;
    transform: translateX(0);
}

.timeline-item.current-year {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), transparent);
    border-radius: 10px;
    padding: 1rem;
}

.timeline-marker {
    flex-shrink: 0;
    width: 120px;
    text-align: center;
    margin-right: 2rem;
}

.timeline-year {
    background: var(--accent-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1.1rem;
}

.timeline-age {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.timeline-content {
    flex: 1;
    background: var(--bg-secondary);
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px var(--shadow-light);
}

.timeline-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.timeline-project-card {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 0.75rem;
    border-left: 3px solid var(--border-color);
    transition: all 0.3s ease;
}

.timeline-project-card.status-completed {
    border-left-color: var(--success);
}

.timeline-project-card.status-in_progress {
    border-left-color: var(--info);
}

.timeline-project-card.status-not_started {
    border-left-color: var(--text-muted);
}

.timeline-project-card:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.project-desc {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0.5rem 0;
}

.insight-stat h3 {
    margin-bottom: 0.25rem;
}

.insight-stat p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Dream Wall Styles - Tile Layout */
.dream-wall-container {
    background: var(--bg-tertiary);
    border-radius: 15px;
    padding: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 10px var(--shadow-light);
}

.dream-wall-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    grid-auto-rows: 200px;
    gap: 15px;
    padding: 10px;
    background: linear-gradient(45deg,
        rgba(102, 126, 234, 0.03) 0%,
        rgba(118, 75, 162, 0.03) 50%,
        rgba(102, 126, 234, 0.03) 100%);
}

/* Dream Tiles - Grid Layout */
.dream-tile {
    position: relative;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-radius: 12px;
    overflow: hidden;
    background: var(--bg-secondary);
    box-shadow: 0 4px 15px var(--shadow-light);
    will-change: transform, filter;
}

/* Tile Sizes - Like Bathroom Tiles */
.tile-size-small {
    grid-column: span 1;
    grid-row: span 1;
}

.tile-size-medium {
    grid-column: span 1;
    grid-row: span 2;
}

.tile-size-large {
    grid-column: span 2;
    grid-row: span 1;
}

.tile-size-extra-large {
    grid-column: span 2;
    grid-row: span 2;
}

/* Responsive tile sizes */
@media (max-width: 768px) {
    .dream-wall-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        grid-auto-rows: 150px;
        gap: 10px;
        padding: 10px;
    }

    .tile-size-large,
    .tile-size-extra-large {
        grid-column: span 1;
    }

    .tile-title {
        font-size: 0.8rem;
    }

    .tile-action-btn {
        width: 28px;
        height: 28px;
    }
}

@media (max-width: 480px) {
    .dream-wall-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        grid-auto-rows: 120px;
        gap: 8px;
    }

    .tile-placeholder i {
        font-size: 1.8rem;
    }

    .placeholder-text {
        font-size: 0.7rem;
    }
}

/* Tile Content */
.tile-content {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.dream-tile:hover .tile-content {
    transform: scale(1.02);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.tile-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.tile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s ease;
    background: var(--bg-tertiary);
}

.tile-image[src=""], .tile-image:not([src]) {
    display: none;
}

.dream-tile:hover .tile-image {
    transform: scale(1.1);
    filter: brightness(0.8);
}

/* Loading state for images */
.tile-image {
    opacity: 0;
    transition: opacity 0.5s ease;
}

.tile-image.loaded {
    opacity: 1;
}

/* Loading spinner for images */
.tile-image-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: imageSpinner 1s linear infinite;
    z-index: 2;
    opacity: 0;
}

.tile-image-container.loading::before {
    opacity: 1;
}

.tile-image-container.loaded::before {
    opacity: 0;
}

@keyframes imageSpinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.tile-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    text-align: center;
    transition: all 0.3s ease;
}

.dream-tile:hover .tile-placeholder {
    background: linear-gradient(135deg, var(--accent-secondary), var(--accent-primary));
}

.tile-placeholder i {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
    animation: placeholderPulse 2s ease-in-out infinite;
}

.placeholder-text {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes placeholderPulse {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.1); opacity: 1; }
}

/* Tile Overlay */
.tile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        transparent 50%,
        rgba(0, 0, 0, 0.7) 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.dream-tile:hover .tile-overlay {
    opacity: 1;
}

.tile-info {
    margin-top: auto;
}

.tile-title {
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

.tile-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.priority-indicator {
    color: #ffd700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.target-date {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.7rem;
}

.tile-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 0.5rem;
}

.tile-action-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.tile-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: scale(1.1);
}

/* Add Dream Tile */
.add-dream-tile {
    border: 2px dashed var(--border-color);
    background: transparent;
    transition: all 0.3s ease;
}

.add-dream-tile:hover {
    border-color: var(--accent-primary);
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.02);
}

.add-tile-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-decoration: none;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.add-tile-content:hover {
    color: var(--accent-primary);
    text-decoration: none;
}

.add-tile-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.add-dream-tile:hover .add-tile-icon {
    opacity: 1;
    transform: scale(1.1);
}

.add-tile-text h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.add-tile-text p {
    font-size: 0.8rem;
    opacity: 0.8;
    margin: 0;
}

/* Category-based tile accents */
.category-travel .tile-placeholder {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

.category-career .tile-placeholder {
    background: linear-gradient(135deg, #6f42c1, #007bff);
}

.category-lifestyle .tile-placeholder {
    background: linear-gradient(135deg, #e83e8c, #fd7e14);
}

.category-health .tile-placeholder {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.category-education .tile-placeholder {
    background: linear-gradient(135deg, #007bff, #6f42c1);
}

.category-material .tile-placeholder {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.category-experiences .tile-placeholder {
    background: linear-gradient(135deg, #20c997, #17a2b8);
}

.category-relationships .tile-placeholder {
    background: linear-gradient(135deg, #e83e8c, #dc3545);
}

/* Priority-based effects */
.priority-urgent {
    animation: urgentPulse 2s ease-in-out infinite;
}

.priority-high {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
}

@keyframes urgentPulse {
    0%, 100% { box-shadow: 0 4px 15px var(--shadow-light); }
    50% { box-shadow: 0 4px 25px rgba(220, 53, 69, 0.4); }
}

.dream-stats {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.dream-controls {
    display: flex;
    align-items: center;
}

/* Achieved Dreams */
.achieved-dream-card {
    background: var(--bg-secondary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px var(--shadow-light);
    transition: all 0.3s ease;
    position: relative;
}

.achieved-dream-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.achieved-dream-image {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.achieved-dream-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: sepia(20%) saturate(1.2);
}

.achievement-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(40, 167, 69, 0.9);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.achieved-dream-content {
    padding: 1rem;
}

.achieved-dream-content h6 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Advanced Dream Wall Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-5px) rotate(1deg); }
    50% { transform: translateY(-3px) rotate(-1deg); }
    75% { transform: translateY(-7px) rotate(0.5deg); }
}

@keyframes dreamPulse {
    0%, 100% { box-shadow: 0 4px 20px var(--shadow-light); }
    50% { box-shadow: 0 8px 30px var(--accent-primary); }
}

@keyframes dreamGlow {
    0%, 100% { filter: brightness(1) saturate(1); }
    50% { filter: brightness(1.1) saturate(1.2); }
}

@keyframes dreamShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.dream-tile.priority-high {
    animation: dreamPulse 3s ease-in-out infinite;
}

.dream-tile.priority-medium {
    animation: dreamGlow 4s ease-in-out infinite;
}

.dream-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
    z-index: 1;
}

.dream-tile:hover .dream-content::before {
    left: 100%;
}

/* Particle Background for Dream Wall */
.dream-wall::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.05) 0%, transparent 50%);
    animation: particleMove 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes particleMove {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

@keyframes dreamBreathe {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.02) rotate(0.5deg); }
}

@keyframes dreamWobble {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(1deg); }
    75% { transform: rotate(-1deg); }
}

@keyframes dreamBounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.dream-tile.animate-breathe {
    animation: dreamBreathe 4s ease-in-out infinite;
}

.dream-tile.animate-wobble {
    animation: dreamWobble 3s ease-in-out infinite;
}

.dream-tile.animate-bounce {
    animation: dreamBounce 2s ease-in-out infinite;
}

/* Morphing shapes for dream tiles */
.dream-content.shape-circle {
    border-radius: 50%;
}

.dream-content.shape-rounded {
    border-radius: 20px;
}

.dream-content.shape-diamond {
    border-radius: 15px;
    transform: rotate(45deg);
}

.dream-content.shape-diamond .dream-image,
.dream-content.shape-diamond .dream-placeholder,
.dream-content.shape-diamond .dream-info {
    transform: rotate(-45deg);
}

/* Gradient overlays for different priorities */
.dream-tile.priority-urgent::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(220, 53, 69, 0.1), transparent);
    border-radius: inherit;
    pointer-events: none;
    z-index: 1;
}

.dream-tile.priority-high::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 193, 7, 0.1), transparent);
    border-radius: inherit;
    pointer-events: none;
    z-index: 1;
}
}

/* Category Badge Colors */
.badge.bg-travel { background-color: #17a2b8 !important; }
.badge.bg-career { background-color: #6f42c1 !important; }
.badge.bg-lifestyle { background-color: #e83e8c !important; }
.badge.bg-relationships { background-color: #fd7e14 !important; }
.badge.bg-health { background-color: #28a745 !important; }
.badge.bg-education { background-color: #007bff !important; }
.badge.bg-material { background-color: #ffc107 !important; color: #000 !important; }
.badge.bg-experiences { background-color: #20c997 !important; }
.badge.bg-other { background-color: #6c757d !important; }

/* Empty State */
.empty-state {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
}

.empty-state i {
    opacity: 0.5;
}

.empty-state h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

/* Responsive Dream Wall */
@media (max-width: 768px) {
    .dream-wall {
        height: 400px;
    }

    .dream-tile {
        width: 150px;
    }

    .dream-image, .dream-placeholder {
        height: 80px;
    }

    .dream-info {
        padding: 0.75rem;
    }

    .dream-title {
        font-size: 0.8rem;
    }

    .dream-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .dream-controls {
        margin-top: 1rem;
    }
}

/* Image Preview Styles */
.image-preview-container {
    position: relative;
    display: inline-block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.image-preview-container:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.preview-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: white;
    padding: 0.5rem;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-preview-container:hover .preview-overlay {
    opacity: 1;
}

/* Enhanced form styling */
.form-control:focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* Sticky navbar adjustments */
.sticky-top {
    backdrop-filter: blur(10px);
    background-color: rgba(52, 58, 64, 0.95) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Content padding for sticky navbar */
body {
    padding-top: 0;
}

.container {
    padding-top: 1rem;
}

/* Responsive Profile Settings */
@media (max-width: 768px) {
    .profile-header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .profile-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .profile-avatar {
        font-size: 3rem;
    }

    .profile-name {
        font-size: 1.5rem;
    }

    .settings-section {
        padding: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-actions .btn {
        width: 100%;
    }

    .timeline-actions {
        flex-direction: column;
    }

    .timeline-actions .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .profile-header-card {
        padding: 1.5rem;
        border-radius: 15px;
    }

    .profile-stats {
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 1.4rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stat-value {
        font-size: 1.2rem;
    }
}

/* Life Timeline Styles */
.life-timeline-container {
    padding: 1rem 0;
}

.life-progress-bar {
    position: relative;
    margin: 1rem 0;
}

.progress-container {
    position: relative;
    height: 60px;
    background-color: #e9ecef;
    border-radius: 30px;
    overflow: hidden;
    border: 2px solid #dee2e6;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #ffc107 100%);
    border-radius: 30px;
    position: relative;
    transition: width 0.5s ease-in-out;
    overflow: hidden;
}

.sand-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.3) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.3) 2px, transparent 2px),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.2) 1px, transparent 1px);
    background-size: 20px 20px, 30px 30px, 15px 15px;
    animation: sandFlow 3s linear infinite;
}

@keyframes sandFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: #333;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
    z-index: 10;
}

.timeline-markers {
    display: flex;
    justify-content: space-between;
    position: relative;
    font-size: 0.8rem;
    color: #6c757d;
}

.marker {
    position: absolute;
    transform: translateX(-50%);
    font-weight: bold;
}

/* Project Cards */
.project-card {
    border-left: 4px solid #007bff;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.project-progress .progress {
    height: 8px;
    margin-bottom: 0.25rem;
}

/* Weeks View Styles */
.weeks-grid {
    display: grid;
    grid-template-columns: repeat(52, 1fr);
    gap: 2px;
    margin: 1rem 0;
}

.week-cell {
    width: 12px;
    height: 12px;
    border: 1px solid #dee2e6;
    border-radius: 2px;
    transition: all 0.2s ease;
}

.week-cell.lived {
    background-color: #28a745;
    border-color: #1e7e34;
}

.week-cell.future {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.week-cell:hover {
    transform: scale(1.2);
    z-index: 10;
    position: relative;
}

.year-label {
    font-size: 0.8rem;
    font-weight: bold;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Card Styles */
.card {
    border-radius: 10px;
    border: none;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 2px 4px var(--shadow-light);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px var(--shadow-medium);
}

.card-header {
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    border-radius: 10px 10px 0 0 !important;
    color: var(--text-primary);
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--shadow-medium);
}

.theme-toggle:hover {
    transform: scale(1.1);
    background: var(--accent-secondary);
}

/* Priority badges */
.badge.bg-low { background-color: #6c757d !important; }
.badge.bg-medium { background-color: #ffc107 !important; }
.badge.bg-high { background-color: #fd7e14 !important; }
.badge.bg-urgent { background-color: #dc3545 !important; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .progress-container {
        height: 40px;
    }
    
    .progress-text {
        font-size: 0.8rem;
    }
    
    .weeks-grid {
        grid-template-columns: repeat(26, 1fr);
    }
    
    .week-cell {
        width: 8px;
        height: 8px;
    }
}

/* Dark theme profile stats */
[data-theme="dark"] .stat-number {
    color: #f8f9fa !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

[data-theme="dark"] .stat-label {
    color: #adb5bd !important;
}
