{% extends 'base.html' %}

{% block title %}Dream Wall - TimeLine{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="hero-section text-center py-4">
            <h1 class="display-5 mb-3">
                <i class="fas fa-star text-warning"></i> Your Dream Wall
            </h1>
            <p class="lead">Visualize your dreams and aspirations</p>
        </div>
    </div>
</div>

<!-- Dream Wall Controls -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="dream-stats">
            <span class="stat-item">
                <i class="fas fa-star text-warning"></i>
                <strong>{{ total_dreams }}</strong> Dreams
            </span>
            <span class="stat-item">
                <i class="fas fa-trophy text-success"></i>
                <strong>{{ achieved_count }}</strong> Achieved
            </span>
            <span class="stat-item">
                <i class="fas fa-fire text-danger"></i>
                <strong>{{ active_dreams|length }}</strong> Active
            </span>
        </div>
    </div>
    <div class="col-md-4 text-end">
        <div class="dream-controls">
            <button id="randomize-btn" class="btn btn-outline-primary me-2">
                <i class="fas fa-random"></i> Shuffle
            </button>
            <a href="{% url 'dream_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Dream
            </a>
        </div>
    </div>
</div>

<!-- Dream Wall Canvas -->
<div class="dream-wall-container">
    <div id="dream-wall" class="dream-wall">
        {% for dream in active_dreams %}
        <div class="dream-tile" 
             data-id="{{ dream.id }}"
             style="left: {{ dream.position_x }}%; 
                    top: {{ dream.position_y }}%; 
                    transform: scale({{ dream.size_factor }}) rotate({{ dream.rotation }}deg);">
            
            <div class="dream-content">
                {% if dream.image_url %}
                <div class="dream-image">
                    <img src="{{ dream.image_url }}" alt="{{ dream.title }}" loading="lazy">
                </div>
                {% else %}
                <div class="dream-placeholder">
                    <i class="fas fa-star"></i>
                </div>
                {% endif %}
                
                <div class="dream-info">
                    <h6 class="dream-title">{{ dream.title }}</h6>
                    <span class="dream-category badge bg-{{ dream.category }}">{{ dream.get_category_display }}</span>
                    <div class="dream-priority">
                        {% for i in "12345678910"|make_list %}
                            {% if forloop.counter <= dream.priority %}
                                <i class="fas fa-star text-warning"></i>
                            {% else %}
                                <i class="far fa-star text-muted"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <div class="dream-actions">
                    <a href="{% url 'dream_edit' dream.id %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="{% url 'dream_achieve' dream.id %}" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-check"></i>
                    </a>
                    <a href="{% url 'dream_delete' dream.id %}" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-trash"></i>
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Achieved Dreams Section -->
{% if achieved_dreams %}
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy text-success"></i> Achieved Dreams
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for dream in achieved_dreams %}
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="achieved-dream-card">
                            {% if dream.image_url %}
                            <div class="achieved-dream-image">
                                <img src="{{ dream.image_url }}" alt="{{ dream.title }}">
                                <div class="achievement-overlay">
                                    <i class="fas fa-trophy"></i>
                                </div>
                            </div>
                            {% endif %}
                            <div class="achieved-dream-content">
                                <h6>{{ dream.title }}</h6>
                                <small class="text-success">
                                    <i class="fas fa-check"></i> Achieved {{ dream.achieved_date|date:"M d, Y" }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Empty State -->
{% if not active_dreams and not achieved_dreams %}
<div class="empty-state text-center py-5">
    <i class="fas fa-star fa-4x text-muted mb-3"></i>
    <h3>Your Dream Wall is Empty</h3>
    <p class="text-muted">Start adding your dreams and aspirations to create your personal vision board!</p>
    <a href="{% url 'dream_create' %}" class="btn btn-primary btn-lg">
        <i class="fas fa-plus"></i> Add Your First Dream
    </a>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dreamWall = document.getElementById('dream-wall');
    const randomizeBtn = document.getElementById('randomize-btn');
    
    // Animate dream tiles on load
    const dreamTiles = document.querySelectorAll('.dream-tile');
    dreamTiles.forEach((tile, index) => {
        tile.style.opacity = '0';
        tile.style.transform += ' scale(0.5)';
        
        setTimeout(() => {
            tile.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            tile.style.opacity = '1';
            tile.style.transform = tile.style.transform.replace('scale(0.5)', '');
        }, index * 100);
    });
    
    // Randomize positions
    if (randomizeBtn) {
        randomizeBtn.addEventListener('click', function() {
            fetch('{% url "randomize_dreams" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    location.reload();
                }
            });
        });
    }
    
    // Add floating animation
    dreamTiles.forEach(tile => {
        const randomDelay = Math.random() * 2;
        const randomDuration = 3 + Math.random() * 2;
        
        tile.style.animation = `float ${randomDuration}s ease-in-out ${randomDelay}s infinite`;
    });
    
    // Hover effects
    dreamTiles.forEach(tile => {
        tile.addEventListener('mouseenter', function() {
            this.style.transform += ' scale(1.1)';
            this.style.zIndex = '1000';
        });
        
        tile.addEventListener('mouseleave', function() {
            this.style.transform = this.style.transform.replace(' scale(1.1)', '');
            this.style.zIndex = 'auto';
        });
    });
});
</script>
{% endblock %}
