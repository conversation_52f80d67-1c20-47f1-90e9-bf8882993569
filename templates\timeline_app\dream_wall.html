{% extends 'base.html' %}

{% block title %}Dream Wall - TimeLine{% endblock %}

{% block content %}
{% csrf_token %}
<div class="row mb-4">
    <div class="col-12">
        <div class="hero-section text-center py-4">
            <h1 class="display-5 mb-3">
                <i class="fas fa-star text-warning"></i> Your Dream Wall
            </h1>
            <p class="lead">Visualize your dreams and aspirations</p>
        </div>
    </div>
</div>

<!-- Dream Wall Controls -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="dream-stats">
            <span class="stat-item">
                <i class="fas fa-star text-warning"></i>
                <strong>{{ total_dreams }}</strong> Dreams
            </span>
            <span class="stat-item">
                <i class="fas fa-trophy text-success"></i>
                <strong>{{ achieved_count }}</strong> Achieved
            </span>
            <span class="stat-item">
                <i class="fas fa-fire text-danger"></i>
                <strong>{{ active_dreams|length }}</strong> Active
            </span>
        </div>
    </div>
    <div class="col-md-4 text-end">
        <div class="dream-controls">
            <button id="randomize-btn" class="btn btn-outline-primary me-2">
                <i class="fas fa-random"></i> Shuffle
            </button>
            <a href="{% url 'dream_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Dream
            </a>
        </div>
    </div>
</div>

<!-- Dream Wall Canvas - Tile Layout -->
<div class="dream-wall-container">
    <div id="dream-wall" class="dream-wall-grid">
        {% for dream in active_dreams %}
        <div class="dream-tile
                    tile-size-{{ forloop.counter0|add:dream.priority|divisibleby:3|yesno:'large,medium' }}
                    {% if dream.priority >= 9 %}priority-urgent{% elif dream.priority >= 7 %}priority-high{% elif dream.priority >= 5 %}priority-medium{% endif %}
                    category-{{ dream.category }}"
             data-id="{{ dream.id }}"
             data-priority="{{ dream.priority }}"
             data-category="{{ dream.category }}"
             data-title="{{ dream.title }}">

            <div class="tile-content">
                <div class="tile-image-container">
                    {% if dream.image_display_url %}
                    <img src="{{ dream.image_display_url }}" alt="{{ dream.title }}" class="tile-image" loading="lazy"
                         onerror="this.style.display='none'; this.parentNode.querySelector('.tile-placeholder').style.display='flex';">
                    {% endif %}
                    <div class="tile-placeholder" {% if dream.image_display_url %}style="display: none;"{% endif %}>
                        <i class="fas fa-star"></i>
                        <span class="placeholder-text">{{ dream.get_category_display }}</span>
                    </div>
                </div>

                <div class="tile-overlay">
                    <div class="tile-info">
                        <h6 class="tile-title">{{ dream.title }}</h6>
                        <div class="tile-meta">
                            <span class="priority-indicator">
                                {% for i in "12345"|make_list %}
                                    {% if forloop.counter <= dream.priority|floatformat:0|add:0|div:2 %}
                                        <i class="fas fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </span>
                            {% if dream.target_date %}
                                <span class="target-date">
                                    <i class="fas fa-calendar"></i> {{ dream.target_date|date:"M Y" }}
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="tile-actions">
                        <a href="{% url 'dream_edit' dream.id %}" class="tile-action-btn" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'dream_achieve' dream.id %}" class="tile-action-btn" title="Mark as Achieved">
                            <i class="fas fa-check"></i>
                        </a>
                        <a href="{% url 'dream_delete' dream.id %}" class="tile-action-btn" title="Delete">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Add Dream Tile -->
        <div class="dream-tile add-dream-tile tile-size-medium">
            <a href="{% url 'dream_create' %}" class="tile-content add-tile-content">
                <div class="add-tile-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="add-tile-text">
                    <h6>Add New Dream</h6>
                    <p>Click to add your next goal</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Achieved Dreams Section -->
{% if achieved_dreams %}
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy text-success"></i> Achieved Dreams
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for dream in achieved_dreams %}
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="achieved-dream-card">
                            {% if dream.image_display_url %}
                            <div class="achieved-dream-image">
                                <img src="{{ dream.image_display_url }}" alt="{{ dream.title }}">
                                <div class="achievement-overlay">
                                    <i class="fas fa-trophy"></i>
                                </div>
                            </div>
                            {% endif %}
                            <div class="achieved-dream-content">
                                <h6>{{ dream.title }}</h6>
                                <small class="text-success">
                                    <i class="fas fa-check"></i> Achieved {{ dream.achieved_date|date:"M d, Y" }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Empty State -->
{% if not active_dreams and not achieved_dreams %}
<div class="empty-state text-center py-5">
    <i class="fas fa-star fa-4x text-muted mb-3"></i>
    <h3>Your Dream Wall is Empty</h3>
    <p class="text-muted">Start adding your dreams and aspirations to create your personal vision board!</p>
    <a href="{% url 'dream_create' %}" class="btn btn-primary btn-lg">
        <i class="fas fa-plus"></i> Add Your First Dream
    </a>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dreamWall = document.getElementById('dream-wall');
    const randomizeBtn = document.getElementById('randomize-btn');

    // Animate dream tiles on load with staggered entrance
    const dreamTiles = document.querySelectorAll('.dream-tile:not(.add-dream-tile)');
    dreamTiles.forEach((tile, index) => {
        tile.style.opacity = '0';
        tile.style.transform = 'scale(0.8) translateY(30px)';

        setTimeout(() => {
            tile.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            tile.style.opacity = '1';
            tile.style.transform = 'scale(1) translateY(0)';
        }, index * 100);
    });

    // Animate add tile separately
    const addTile = document.querySelector('.add-dream-tile');
    if (addTile) {
        addTile.style.opacity = '0';
        addTile.style.transform = 'scale(0.8)';

        setTimeout(() => {
            addTile.style.transition = 'all 0.6s ease';
            addTile.style.opacity = '1';
            addTile.style.transform = 'scale(1)';
        }, dreamTiles.length * 100 + 200);
    }
    
    // Randomize tile layout
    if (randomizeBtn) {
        randomizeBtn.addEventListener('click', function() {
            // Animate tiles out
            dreamTiles.forEach((tile, index) => {
                setTimeout(() => {
                    tile.style.transition = 'all 0.4s ease';
                    tile.style.transform = 'scale(0.8) rotate(10deg)';
                    tile.style.opacity = '0.3';
                }, index * 50);
            });

            // Shuffle and animate back in
            setTimeout(() => {
                shuffleTileLayout();

                dreamTiles.forEach((tile, index) => {
                    setTimeout(() => {
                        tile.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                        tile.style.transform = 'scale(1) rotate(0deg)';
                        tile.style.opacity = '1';
                    }, index * 80);
                });
            }, 500);
        });
    }

    function shuffleTileLayout() {
        const sizes = ['tile-size-small', 'tile-size-medium', 'tile-size-large'];

        dreamTiles.forEach(tile => {
            // Remove current size class
            sizes.forEach(size => tile.classList.remove(size));

            // Add random size class
            const randomSize = sizes[Math.floor(Math.random() * sizes.length)];
            tile.classList.add(randomSize);

            // Random subtle effects
            if (Math.random() > 0.7) {
                tile.style.filter = 'hue-rotate(' + (Math.random() * 30 - 15) + 'deg)';
            } else {
                tile.style.filter = '';
            }
        });
    }
    
    // Add subtle hover effects and priority indicators
    dreamTiles.forEach(tile => {
        const priority = parseInt(tile.dataset.priority) || 5;

        // Add priority-based visual effects
        if (priority >= 9) {
            tile.classList.add('priority-urgent');
        } else if (priority >= 7) {
            tile.classList.add('priority-high');
        }

        // Enhanced hover interaction
        tile.addEventListener('mouseenter', function() {
            this.style.zIndex = '10';
        });

        tile.addEventListener('mouseleave', function() {
            this.style.zIndex = '1';
        });
    });

    // Auto-refresh tile layout every 2 minutes for dynamic feel
    setInterval(() => {
        if (dreamTiles.length > 0) {
            shuffleTileLayout();
        }
    }, 120000); // 2 minutes

});
</script>
{% endblock %}
