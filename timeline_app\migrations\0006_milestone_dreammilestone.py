# Generated by Django 4.2.7 on 2025-07-09 21:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('timeline_app', '0005_dreamitem_image_alter_dreamitem_image_url_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Milestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('target_date', models.DateField(blank=True, help_text='When you want to complete this milestone', null=True)),
                ('is_completed', models.BooleanField(default=False)),
                ('completed_date', models.DateTimeField(blank=True, null=True)),
                ('order', models.PositiveIntegerField(default=0, help_text='Order of milestone in project')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='timeline_app.project')),
            ],
            options={
                'ordering': ['order', 'target_date', 'created_at'],
                'unique_together': {('project', 'order')},
            },
        ),
        migrations.CreateModel(
            name='DreamMilestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('target_date', models.DateField(blank=True, help_text='When you want to complete this milestone', null=True)),
                ('is_completed', models.BooleanField(default=False)),
                ('completed_date', models.DateTimeField(blank=True, null=True)),
                ('order', models.PositiveIntegerField(default=0, help_text='Order of milestone in dream')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('dream', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='timeline_app.dreamitem')),
            ],
            options={
                'ordering': ['order', 'target_date', 'created_at'],
                'unique_together': {('dream', 'order')},
            },
        ),
    ]
