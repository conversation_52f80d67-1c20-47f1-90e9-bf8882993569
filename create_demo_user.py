#!/usr/bin/env python
import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'timeline_project.settings')
django.setup()

from django.contrib.auth.models import User
from timeline_app.models import UserProfile
from datetime import date

# Create demo user
try:
    # Create user
    user = User.objects.create_user(
        username='demo',
        password='demo123',
        email='<EMAIL>',
        first_name='Demo',
        last_name='User'
    )
    
    # Create profile
    profile = UserProfile.objects.create(
        user=user,
        birth_date=date(1990, 6, 15),  # 34 years old
        timezone='US/Eastern',
        theme='light',
        life_expectancy=85,
        bio='Demo user for testing the TimeLine application features.',
        location='New York, USA',
        website='https://timeline-demo.com',
        email_notifications=True,
        weekly_reminders=True
    )
    
    print("✅ Demo user created successfully!")
    print(f"Username: demo")
    print(f"Password: demo123")
    print(f"Age: {profile.age} years")
    print(f"Life Progress: {profile.life_progress_percentage:.1f}%")
    print(f"Timezone: {profile.timezone}")
    
except Exception as e:
    if "UNIQUE constraint failed" in str(e):
        print("Demo user already exists!")
        print("Username: demo")
        print("Password: demo123")
    else:
        print(f"Error: {e}")

# Also create admin user if needed
try:
    admin = User.objects.create_superuser(
        username='admin',
        password='admin123',
        email='<EMAIL>'
    )
    print("✅ Admin user created!")
    print("Username: admin")
    print("Password: admin123")
except Exception as e:
    if "UNIQUE constraint failed" in str(e):
        print("Admin user already exists!")
    else:
        print(f"Admin error: {e}")
