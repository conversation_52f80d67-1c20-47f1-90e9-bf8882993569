"""
Management command to generate daily notifications for all users
Run this daily via cron job or task scheduler
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from timeline_app.notification_service import NotificationService
from timeline_app.models import Notification


class Command(BaseCommand):
    help = 'Generate daily notifications for all users'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=int,
            help='Generate notifications for specific user ID only',
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up expired notifications',
        )
    
    def handle(self, *args, **options):
        if options['cleanup']:
            self.cleanup_expired_notifications()
        
        if options['user_id']:
            try:
                user = User.objects.get(id=options['user_id'])
                self.generate_for_user(user)
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User with ID {options["user_id"]} not found')
                )
        else:
            self.generate_for_all_users()
    
    def generate_for_all_users(self):
        """Generate notifications for all active users"""
        users = User.objects.filter(is_active=True)
        total_notifications = 0
        
        self.stdout.write(f'Generating notifications for {users.count()} users...')
        
        for user in users:
            count = self.generate_for_user(user)
            total_notifications += count
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully generated {total_notifications} notifications for {users.count()} users'
            )
        )
    
    def generate_for_user(self, user):
        """Generate notifications for a specific user"""
        try:
            notifications = NotificationService.generate_daily_notifications(user)
            
            # Bulk create notifications
            created_notifications = Notification.objects.bulk_create(notifications)
            
            if created_notifications:
                self.stdout.write(
                    f'Generated {len(created_notifications)} notifications for {user.username}'
                )
            
            return len(created_notifications)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'Error generating notifications for {user.username}: {str(e)}'
                )
            )
            return 0
    
    def cleanup_expired_notifications(self):
        """Clean up expired and old notifications"""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        
        # Delete expired notifications
        expired_count = Notification.objects.filter(
            expires_at__lt=now
        ).delete()[0]
        
        # Delete old read notifications (older than 30 days)
        old_read_count = Notification.objects.filter(
            is_read=True,
            created_at__lt=now - timedelta(days=30)
        ).delete()[0]
        
        # Delete old dismissed notifications (older than 7 days)
        old_dismissed_count = Notification.objects.filter(
            is_dismissed=True,
            created_at__lt=now - timedelta(days=7)
        ).delete()[0]
        
        total_cleaned = expired_count + old_read_count + old_dismissed_count
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Cleaned up {total_cleaned} old notifications '
                f'(expired: {expired_count}, old read: {old_read_count}, '
                f'old dismissed: {old_dismissed_count})'
            )
        )
