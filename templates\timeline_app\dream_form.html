{% extends 'base.html' %}

{% block title %}{{ title }} - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-star text-warning"></i> {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="post" class="dream-form">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                {{ form.title.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.title }}
                            {% if form.title.errors %}
                                <div class="form-error">
                                    {% for error in form.title.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">
                                {{ form.category.label }}
                            </label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="form-error">
                                    {% for error in form.category.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">
                                {{ form.priority.label }}
                            </label>
                            {{ form.priority }}
                            {% if form.priority.errors %}
                                <div class="form-error">
                                    {% for error in form.priority.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.priority.help_text }}</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="form-error">
                                    {% for error in form.description.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.image_url.id_for_label }}" class="form-label">
                                {{ form.image_url.label }}
                            </label>
                            {{ form.image_url }}
                            {% if form.image_url.errors %}
                                <div class="form-error">
                                    {% for error in form.image_url.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.image_url.help_text }}</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.target_date.id_for_label }}" class="form-label">
                                {{ form.target_date.label }}
                            </label>
                            {{ form.target_date }}
                            {% if form.target_date.errors %}
                                <div class="form-error">
                                    {% for error in form.target_date.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.target_date.help_text }}</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="form-error">
                                    {% for error in form.notes.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Dream
                        </button>
                        <a href="{% url 'dream_wall' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image URL preview
    const imageUrlInput = document.getElementById('id_image_url');
    if (imageUrlInput) {
        imageUrlInput.addEventListener('blur', function() {
            const url = this.value.trim();
            if (url) {
                // Create preview
                let preview = document.getElementById('image-preview');
                if (!preview) {
                    preview = document.createElement('div');
                    preview.id = 'image-preview';
                    preview.className = 'mt-2';
                    this.parentNode.appendChild(preview);
                }
                
                preview.innerHTML = `
                    <div class="image-preview-container">
                        <img src="${url}" alt="Preview" style="max-width: 200px; max-height: 150px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" 
                             onerror="this.parentNode.innerHTML='<small class=\\"text-danger\\">Invalid image URL</small>'">
                    </div>
                `;
            }
        });
    }
    
    // Priority slider visual feedback
    const priorityInput = document.getElementById('id_priority');
    if (priorityInput) {
        function updatePriorityDisplay() {
            const value = parseInt(priorityInput.value);
            let display = document.getElementById('priority-display');
            if (!display) {
                display = document.createElement('div');
                display.id = 'priority-display';
                display.className = 'mt-1';
                priorityInput.parentNode.appendChild(display);
            }
            
            let stars = '';
            for (let i = 1; i <= 10; i++) {
                if (i <= value) {
                    stars += '<i class="fas fa-star text-warning"></i> ';
                } else {
                    stars += '<i class="far fa-star text-muted"></i> ';
                }
            }
            display.innerHTML = stars;
        }
        
        priorityInput.addEventListener('input', updatePriorityDisplay);
        updatePriorityDisplay(); // Initial display
    }
});
</script>
{% endblock %}
