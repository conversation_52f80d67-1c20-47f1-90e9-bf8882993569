{% extends 'base.html' %}

{% block title %}{{ title }} - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-star text-warning"></i> {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" class="dream-form">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                {{ form.title.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.title }}
                            {% if form.title.errors %}
                                <div class="form-error">
                                    {% for error in form.title.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">
                                {{ form.category.label }}
                            </label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="form-error">
                                    {% for error in form.category.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">
                                {{ form.priority.label }}
                            </label>
                            {{ form.priority }}
                            {% if form.priority.errors %}
                                <div class="form-error">
                                    {% for error in form.priority.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.priority.help_text }}</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="form-error">
                                    {% for error in form.description.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.image.id_for_label }}" class="form-label">
                                {{ form.image.label }}
                            </label>
                            {{ form.image }}
                            {% if form.image.errors %}
                                <div class="form-error mt-2">
                                    {% for error in form.image.errors %}
                                        <div class="alert alert-danger py-2 px-3 mb-2">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <small>{{ error }}</small>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Show existing image if editing -->
                            {% if dream and dream.image %}
                                <div class="existing-image-preview mt-2">
                                    <div class="image-preview-container">
                                        <img src="{{ dream.image.url }}" alt="Current image"
                                             style="max-width: 200px; max-height: 150px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); object-fit: cover;">
                                        <div class="preview-overlay">
                                            <small class="text-info">
                                                <i class="fas fa-image"></i> Current image
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            <small class="form-text text-muted">{{ form.image.help_text }}</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.image_url.id_for_label }}" class="form-label">
                                {{ form.image_url.label }}
                            </label>
                            {{ form.image_url }}
                            {% if form.image_url.errors %}
                                <div class="form-error mt-2">
                                    {% for error in form.image_url.errors %}
                                        <div class="alert alert-danger py-2 px-3 mb-2">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <small>{{ error }}</small>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Show existing image URL if editing -->
                            {% if dream and dream.image_url and not dream.image %}
                                <div class="existing-image-preview mt-2">
                                    <div class="image-preview-container">
                                        <img src="{{ dream.image_url }}" alt="Current image"
                                             style="max-width: 200px; max-height: 150px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); object-fit: cover;"
                                             onerror="this.parentNode.innerHTML='<small class=\\"text-warning\\">Current URL image not accessible</small>'">
                                        <div class="preview-overlay">
                                            <small class="text-info">
                                                <i class="fas fa-link"></i> Current URL image
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            <small class="form-text text-muted">{{ form.image_url.help_text }}</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.target_date.id_for_label }}" class="form-label">
                                {{ form.target_date.label }}
                            </label>
                            {{ form.target_date }}
                            {% if form.target_date.errors %}
                                <div class="form-error">
                                    {% for error in form.target_date.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.target_date.help_text }}</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="form-error">
                                    {% for error in form.notes.errors %}
                                        <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Dream
                        </button>
                        <a href="{% url 'dream_wall' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image upload preview
    const imageInput = document.getElementById('id_image');
    const imageUrlInput = document.getElementById('id_image_url');

    function createImagePreview(src, container, isFile = false) {
        let preview = container.querySelector('.image-preview');
        if (!preview) {
            preview = document.createElement('div');
            preview.className = 'image-preview mt-2';
            container.appendChild(preview);
        }

        // Show loading state
        preview.innerHTML = `
            <div class="image-preview-container">
                <div class="preview-loading">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <small class="text-muted ms-2">Loading preview...</small>
                </div>
            </div>
        `;

        // Create image element to test loading
        const testImg = new Image();

        testImg.onload = function() {
            preview.innerHTML = `
                <div class="image-preview-container">
                    <img src="${src}" alt="Preview" style="max-width: 200px; max-height: 150px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); object-fit: cover;">
                    <div class="preview-overlay">
                        <small class="text-success">
                            <i class="fas fa-check-circle"></i>
                            ${isFile ? 'File ready' : 'URL valid'} (${this.naturalWidth}x${this.naturalHeight})
                        </small>
                    </div>
                </div>
            `;
        };

        testImg.onerror = function() {
            let errorMsg = 'Invalid image';
            if (isFile) {
                errorMsg = 'Cannot preview this image format. Please try JPEG, PNG, or GIF.';
            } else {
                errorMsg = 'Cannot load image from this URL. Please check the link.';
            }

            preview.innerHTML = `
                <div class="image-preview-container">
                    <div class="alert alert-danger py-2 px-3 mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        <small>${errorMsg}</small>
                    </div>
                </div>
            `;
        };

        testImg.src = src;
    }

    // File upload preview
    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Show file info
                console.log(`Selected file: ${file.name}, Size: ${(file.size / (1024*1024)).toFixed(2)}MB, Type: ${file.type}`);

                // Check file size
                const maxSize = 10 * 1024 * 1024; // 10MB
                if (file.size > maxSize) {
                    const preview = imageInput.parentNode.querySelector('.image-preview') ||
                                  (() => {
                                      const div = document.createElement('div');
                                      div.className = 'image-preview mt-2';
                                      imageInput.parentNode.appendChild(div);
                                      return div;
                                  })();

                    preview.innerHTML = `
                        <div class="alert alert-danger py-2 px-3 mb-0">
                            <i class="fas fa-exclamation-triangle"></i>
                            <small>File too large (${(file.size / (1024*1024)).toFixed(1)}MB). Maximum size is 10MB.</small>
                        </div>
                    `;
                    return;
                }

                // Check file type
                if (!file.type.startsWith('image/')) {
                    const preview = imageInput.parentNode.querySelector('.image-preview') ||
                                  (() => {
                                      const div = document.createElement('div');
                                      div.className = 'image-preview mt-2';
                                      imageInput.parentNode.appendChild(div);
                                      return div;
                                  })();

                    preview.innerHTML = `
                        <div class="alert alert-danger py-2 px-3 mb-0">
                            <i class="fas fa-exclamation-triangle"></i>
                            <small>Invalid file type. Please select an image file (JPEG, PNG, GIF, WebP).</small>
                        </div>
                    `;
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    createImagePreview(e.target.result, imageInput.parentNode, true);
                };
                reader.onerror = function() {
                    const preview = imageInput.parentNode.querySelector('.image-preview') ||
                                  (() => {
                                      const div = document.createElement('div');
                                      div.className = 'image-preview mt-2';
                                      imageInput.parentNode.appendChild(div);
                                      return div;
                                  })();

                    preview.innerHTML = `
                        <div class="alert alert-danger py-2 px-3 mb-0">
                            <i class="fas fa-exclamation-triangle"></i>
                            <small>Error reading file. Please try a different image.</small>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                // Clear preview if no file selected
                const preview = imageInput.parentNode.querySelector('.image-preview');
                if (preview) {
                    preview.remove();
                }
            }
        });
    }

    // Image URL preview
    if (imageUrlInput) {
        imageUrlInput.addEventListener('blur', function() {
            const url = this.value.trim();
            if (url) {
                // Basic URL validation
                try {
                    new URL(url);
                    createImagePreview(url, this.parentNode, false);
                } catch (e) {
                    const preview = this.parentNode.querySelector('.image-preview') ||
                                  (() => {
                                      const div = document.createElement('div');
                                      div.className = 'image-preview mt-2';
                                      this.parentNode.appendChild(div);
                                      return div;
                                  })();

                    preview.innerHTML = `
                        <div class="alert alert-danger py-2 px-3 mb-0">
                            <i class="fas fa-exclamation-triangle"></i>
                            <small>Invalid URL format. Please enter a valid image URL.</small>
                        </div>
                    `;
                }
            } else {
                // Clear preview if URL is empty
                const preview = this.parentNode.querySelector('.image-preview');
                if (preview) {
                    preview.remove();
                }
            }
        });
    }
    
    // Priority slider visual feedback
    const priorityInput = document.getElementById('id_priority');
    if (priorityInput) {
        function updatePriorityDisplay() {
            const value = parseInt(priorityInput.value);
            let display = document.getElementById('priority-display');
            if (!display) {
                display = document.createElement('div');
                display.id = 'priority-display';
                display.className = 'mt-1';
                priorityInput.parentNode.appendChild(display);
            }
            
            let stars = '';
            for (let i = 1; i <= 10; i++) {
                if (i <= value) {
                    stars += '<i class="fas fa-star text-warning"></i> ';
                } else {
                    stars += '<i class="far fa-star text-muted"></i> ';
                }
            }
            display.innerHTML = stars;
        }
        
        priorityInput.addEventListener('input', updatePriorityDisplay);
        updatePriorityDisplay(); // Initial display
    }
});
</script>
{% endblock %}
