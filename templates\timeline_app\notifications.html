{% extends 'base.html' %}

{% block title %}Notifications - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-bell"></i> Notifications</h2>
                <a href="{% url 'dashboard' %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="row">
        <div class="col-12">
            {% if notifications %}
                {% for notification in notifications %}
                    <div class="notification-card mb-3" data-notification-id="{{ notification.id }}">
                        <div class="notification-header">
                            <div class="notification-icon">
                                <i class="{{ notification.priority_icon }} {{ notification.priority_color }}"></i>
                            </div>
                            <div class="notification-meta">
                                <h5 class="notification-title">{{ notification.title }}</h5>
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> {{ notification.created_at|timesince }} ago
                                    {% if notification.priority != 'low' %}
                                        <span class="badge bg-{{ notification.priority }} ms-2">{{ notification.get_priority_display }}</span>
                                    {% endif %}
                                </small>
                            </div>
                            <div class="notification-actions">
                                <button class="btn btn-sm btn-outline-secondary" onclick="dismissNotification({{ notification.id }})">
                                    <i class="fas fa-times"></i> Dismiss
                                </button>
                            </div>
                        </div>
                        <div class="notification-body">
                            <p class="notification-message">{{ notification.message }}</p>
                            {% if notification.action_url %}
                                <div class="notification-action">
                                    <a href="{{ notification.action_url }}" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt"></i> {{ notification.action_text|default:"View" }}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                        {% if notification.related_project or notification.related_dream %}
                            <div class="notification-footer">
                                <small class="text-muted">
                                    Related to: 
                                    {% if notification.related_project %}
                                        <a href="{% url 'project_detail' notification.related_project.id %}">
                                            <i class="fas fa-project-diagram"></i> {{ notification.related_project.title }}
                                        </a>
                                    {% elif notification.related_dream %}
                                        <a href="{% url 'dream_detail' notification.related_dream.id %}">
                                            <i class="fas fa-star"></i> {{ notification.related_dream.title }}
                                        </a>
                                    {% endif %}
                                </small>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-bell-slash"></i>
                    </div>
                    <h4>No Notifications</h4>
                    <p class="text-muted">You're all caught up! No new notifications at this time.</p>
                    <a href="{% url 'dashboard' %}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.notification-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.notification-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.notification-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem 1.5rem 0;
}

.notification-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.notification-meta {
    flex-grow: 1;
}

.notification-title {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.notification-actions {
    flex-shrink: 0;
}

.notification-body {
    padding: 0 1.5rem 1rem;
    margin-left: 66px; /* Align with title */
}

.notification-message {
    margin-bottom: 1rem;
    color: #5a6c7d;
    line-height: 1.5;
}

.notification-action {
    margin-bottom: 0.5rem;
}

.notification-footer {
    padding: 0.75rem 1.5rem;
    margin-left: 66px;
    border-top: 1px solid #f1f3f4;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #6c757d;
    margin-bottom: 1rem;
}

/* Priority badge colors */
.badge.bg-urgent {
    background-color: #dc3545 !important;
}

.badge.bg-high {
    background-color: #fd7e14 !important;
}

.badge.bg-medium {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-low {
    background-color: #17a2b8 !important;
}

/* Dark theme support */
[data-theme="dark"] .notification-card {
    background: #212529;
    border-color: #495057;
}

[data-theme="dark"] .notification-icon {
    background: #343a40;
}

[data-theme="dark"] .notification-title {
    color: #f8f9fa;
}

[data-theme="dark"] .notification-message {
    color: #adb5bd;
}

[data-theme="dark"] .notification-footer {
    background: #343a40;
    border-top-color: #495057;
}

[data-theme="dark"] .empty-icon {
    color: #495057;
}

[data-theme="dark"] .empty-state h4 {
    color: #adb5bd;
}
</style>

<script>
function dismissNotification(notificationId) {
    fetch(`/notifications/${notificationId}/dismiss/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Remove notification from DOM with animation
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.style.transition = 'all 0.3s ease';
                notificationElement.style.opacity = '0';
                notificationElement.style.transform = 'translateX(100%)';
                
                setTimeout(() => {
                    notificationElement.remove();
                    
                    // Check if no notifications left
                    const remainingNotifications = document.querySelectorAll('.notification-card');
                    if (remainingNotifications.length === 0) {
                        // Show empty state
                        location.reload();
                    }
                }, 300);
            }
        }
    })
    .catch(error => {
        console.error('Error dismissing notification:', error);
    });
}
</script>
{% endblock %}
