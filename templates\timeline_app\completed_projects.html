{% extends 'base.html' %}

{% block title %}Completed Goals - TimeLine{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="hero-section text-center py-5">
            <h1 class="display-4 mb-3">
                <i class="fas fa-trophy text-warning"></i> Your Achievements
            </h1>
            <p class="lead">Celebrate your completed goals and milestones</p>
        </div>
    </div>
</div>

<!-- Achievement Stats -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="achievement-card">
            <div class="achievement-icon bg-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="achievement-content">
                <h3 class="achievement-number">{{ total_completed }}</h3>
                <p class="achievement-label">Goals Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="achievement-card">
            <div class="achievement-icon bg-primary">
                <i class="fas fa-fire"></i>
            </div>
            <div class="achievement-content">
                <h3 class="achievement-number">{{ completion_streak }}</h3>
                <p class="achievement-label">Completion Streak</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="achievement-card">
            <div class="achievement-icon bg-info">
                <i class="fas fa-calendar"></i>
            </div>
            <div class="achievement-content">
                <h3 class="achievement-number">{{ projects_by_year|length }}</h3>
                <p class="achievement-label">Active Years</p>
            </div>
        </div>
    </div>
</div>

{% if completed_projects %}
<!-- Completed Projects by Year -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Achievement Timeline
                </h5>
            </div>
            <div class="card-body">
                {% for year, projects in projects_by_year.items %}
                <div class="year-section mb-4">
                    <h4 class="year-header">
                        <i class="fas fa-calendar-alt"></i> {{ year }}
                        <span class="badge bg-success ms-2">{{ projects|length }} completed</span>
                    </h4>
                    
                    <div class="row">
                        {% for project in projects %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="completed-project-card">
                                <div class="project-header">
                                    <h6 class="project-title">{{ project.title }}</h6>
                                    <span class="completion-date">
                                        <i class="fas fa-check"></i> {{ project.updated_at|date:"M d" }}
                                    </span>
                                </div>
                                
                                {% if project.description %}
                                <p class="project-description">{{ project.description|truncatewords:15 }}</p>
                                {% endif %}
                                
                                <div class="project-meta">
                                    <span class="badge bg-{{ project.priority }}">{{ project.get_priority_display }}</span>
                                    {% if project.category %}
                                        <span class="badge bg-secondary">{{ project.category.name }}</span>
                                    {% endif %}
                                </div>
                                
                                <div class="project-actions mt-2">
                                    <a href="{% url 'project_edit' project.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% if not forloop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- Empty State -->
<div class="row">
    <div class="col-12">
        <div class="empty-state text-center py-5">
            <i class="fas fa-trophy fa-4x text-muted mb-3"></i>
            <h3>No Completed Goals Yet</h3>
            <p class="text-muted">Start working on your projects and come back to celebrate your achievements!</p>
            <a href="{% url 'project_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Your First Project
            </a>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate achievement cards
    const cards = document.querySelectorAll('.achievement-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
    
    // Animate project cards
    const projectCards = document.querySelectorAll('.completed-project-card');
    projectCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.4s ease';
            card.style.opacity = '1';
            card.style.transform = 'scale(1)';
        }, 500 + (index * 100));
    });
});
</script>
{% endblock %}
