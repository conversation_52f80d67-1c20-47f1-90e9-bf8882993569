{% extends 'base.html' %}
{% load timeline_extras %}

{% block title %}Analytics Dashboard - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="analytics-header">
                <h2><i class="fas fa-chart-line"></i> Analytics Dashboard</h2>
                <p class="text-muted">Deep insights into your goal achievement patterns</p>
            </div>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="metric-card">
                <div class="metric-icon bg-primary">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="metric-content">
                    <h3>{{ project_analytics.completion_rate_trend.0.completed|default:0 }}</h3>
                    <p>Projects Completed</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up"></i> This month
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="metric-card">
                <div class="metric-icon bg-success">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="metric-content">
                    <h3>{{ time_analytics.streak_data.current_streak }}</h3>
                    <p>Day Streak</p>
                    <small class="text-info">
                        Best: {{ time_analytics.streak_data.longest_streak }} days
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="metric-card">
                <div class="metric-icon bg-warning">
                    <i class="fas fa-gauge-high"></i>
                </div>
                <div class="metric-content">
                    <h3>{{ milestone_analytics.productivity_score }}%</h3>
                    <p>Productivity Score</p>
                    <small class="text-muted">
                        Last 30 days
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="metric-card">
                <div class="metric-icon bg-info">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="metric-content">
                    <h3>{{ project_analytics.avg_project_duration|floatformat:0 }}</h3>
                    <p>Avg Days/Project</p>
                    <small class="text-muted">
                        Completion time
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-area"></i> Monthly Progress Trend</h6>
                </div>
                <div class="card-body">
                    <canvas id="progressChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Project Status</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Insights Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Success Patterns</h6>
                </div>
                <div class="card-body">
                    {% if achievement_insights.success_patterns %}
                        {% for pattern in achievement_insights.success_patterns %}
                            <div class="insight-item">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>{{ pattern }}</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Complete more projects to discover your success patterns!</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-rocket"></i> Improvement Suggestions</h6>
                </div>
                <div class="card-body">
                    {% if achievement_insights.improvement_suggestions %}
                        {% for suggestion in achievement_insights.improvement_suggestions %}
                            <div class="insight-item">
                                <i class="fas fa-arrow-up text-primary"></i>
                                <span>{{ suggestion }}</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Keep up the great work! No immediate improvements needed.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Time Analytics Row -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-calendar-day"></i> Peak Performance</h6>
                </div>
                <div class="card-body text-center">
                    <div class="peak-time-display">
                        <div class="peak-day">
                            <h4>{{ time_analytics.most_productive_day }}</h4>
                            <p class="text-muted">Most Productive Day</p>
                        </div>
                        <div class="peak-hour">
                            <h4>{{ time_analytics.most_productive_hour }}</h4>
                            <p class="text-muted">Most Productive Hour</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-target"></i> Goal Achievement</h6>
                </div>
                <div class="card-body text-center">
                    <div class="achievement-display">
                        <div class="achievement-rate">
                            <h4>{{ achievement_insights.dream_completion_rate|floatformat:1 }}%</h4>
                            <p class="text-muted">Dream Achievement Rate</p>
                        </div>
                        <div class="milestone-avg">
                            <h4>{{ achievement_insights.average_milestones_per_project|floatformat:1 }}</h4>
                            <p class="text-muted">Avg Milestones/Project</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-stopwatch"></i> Timing Stats</h6>
                </div>
                <div class="card-body text-center">
                    <div class="timing-display">
                        <div class="completion-time">
                            <h4>{{ milestone_analytics.avg_completion_time|floatformat:1 }}</h4>
                            <p class="text-muted">Avg Days Early/Late</p>
                        </div>
                        <div class="weekly-activity">
                            <h4>{{ milestone_analytics.milestones_this_week }}</h4>
                            <p class="text-muted">Milestones This Week</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Breakdown -->
    <div class="row">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-list-check"></i> Project Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="breakdown-item">
                        <span class="breakdown-label">Active Projects</span>
                        <span class="breakdown-value">{{ category_analytics.projects.active }}</span>
                        <div class="breakdown-bar">
                            <div class="breakdown-fill bg-primary" style="width: {{ category_analytics.projects.active|multiply:10 }}%"></div>
                        </div>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Completed Projects</span>
                        <span class="breakdown-value">{{ category_analytics.projects.completed }}</span>
                        <div class="breakdown-bar">
                            <div class="breakdown-fill bg-success" style="width: {{ category_analytics.projects.completed|multiply:10 }}%"></div>
                        </div>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">On Hold</span>
                        <span class="breakdown-value">{{ category_analytics.projects.on_hold }}</span>
                        <div class="breakdown-bar">
                            <div class="breakdown-fill bg-warning" style="width: {{ category_analytics.projects.on_hold|multiply:10 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-star"></i> Dream Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="breakdown-item">
                        <span class="breakdown-label">High Priority</span>
                        <span class="breakdown-value">{{ category_analytics.dreams.high_priority }}</span>
                        <div class="breakdown-bar">
                            <div class="breakdown-fill bg-danger" style="width: {{ category_analytics.dreams.high_priority|multiply:10 }}%"></div>
                        </div>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Medium Priority</span>
                        <span class="breakdown-value">{{ category_analytics.dreams.medium_priority }}</span>
                        <div class="breakdown-bar">
                            <div class="breakdown-fill bg-warning" style="width: {{ category_analytics.dreams.medium_priority|multiply:10 }}%"></div>
                        </div>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Low Priority</span>
                        <span class="breakdown-value">{{ category_analytics.dreams.low_priority }}</span>
                        <div class="breakdown-bar">
                            <div class="breakdown-fill bg-info" style="width: {{ category_analytics.dreams.low_priority|multiply:10 }}%"></div>
                        </div>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Achieved</span>
                        <span class="breakdown-value">{{ category_analytics.dreams.achieved }}</span>
                        <div class="breakdown-bar">
                            <div class="breakdown-fill bg-success" style="width: {{ category_analytics.dreams.achieved|multiply:10 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.analytics-header {
    text-align: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
    height: 100%;
}

.metric-card:hover {
    transform: translateY(-5px);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.metric-content h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
    color: #2c3e50;
}

.metric-content p {
    margin-bottom: 0.25rem;
    color: #5a6c7d;
    font-weight: 500;
}

.insight-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.insight-item:last-child {
    border-bottom: none;
}

.insight-item i {
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.peak-time-display, .achievement-display, .timing-display {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.peak-day, .peak-hour, .achievement-rate, .milestone-avg, .completion-time, .weekly-activity {
    flex: 1;
}

.peak-day h4, .peak-hour h4, .achievement-rate h4, .milestone-avg h4, .completion-time h4, .weekly-activity h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.breakdown-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.breakdown-label {
    font-weight: 500;
    color: #2c3e50;
    flex: 1;
}

.breakdown-value {
    font-weight: bold;
    color: #007bff;
    margin-left: 1rem;
}

.breakdown-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.breakdown-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Dark theme support */
[data-theme="dark"] .analytics-header {
    background: linear-gradient(135deg, #4c6ef5 0%, #7c3aed 100%);
}

[data-theme="dark"] .metric-card {
    background: #212529;
    border: 1px solid #495057;
}

[data-theme="dark"] .metric-content h3 {
    color: #f8f9fa;
}

[data-theme="dark"] .metric-content p {
    color: #adb5bd;
}

[data-theme="dark"] .insight-item {
    border-bottom-color: #495057;
}

[data-theme="dark"] .breakdown-label {
    color: #f8f9fa;
}

[data-theme="dark"] .breakdown-bar {
    background: #495057;
}

[data-theme="dark"] .peak-day h4,
[data-theme="dark"] .peak-hour h4,
[data-theme="dark"] .achievement-rate h4,
[data-theme="dark"] .milestone-avg h4,
[data-theme="dark"] .completion-time h4,
[data-theme="dark"] .weekly-activity h4 {
    color: #f8f9fa;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Progress Chart
    const progressCtx = document.getElementById('progressChart').getContext('2d');
    const progressData = [
        {% for month in time_analytics.monthly_progress %}
            {{ month.completed }},
        {% endfor %}
    ];
    const progressLabels = [
        {% for month in time_analytics.monthly_progress %}
            '{{ month.month }}',
        {% endfor %}
    ];

    new Chart(progressCtx, {
        type: 'line',
        data: {
            labels: progressLabels,
            datasets: [{
                label: 'Milestones Completed',
                data: progressData,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Project Status Pie Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active', 'Completed', 'On Hold'],
            datasets: [{
                data: [
                    {{ category_analytics.projects.active }},
                    {{ category_analytics.projects.completed }},
                    {{ category_analytics.projects.on_hold }}
                ],
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %}
