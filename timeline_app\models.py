from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, datetime
import calendar


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    birth_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Profile"

    @property
    def age(self):
        if self.birth_date:
            today = date.today()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    @property
    def life_progress_percentage(self):
        """Calculate life progress as percentage (assuming 100 years lifespan)"""
        if self.birth_date:
            today = date.today()
            total_days = 100 * 365.25  # Approximate days in 100 years
            days_lived = (today - self.birth_date).days
            return min((days_lived / total_days) * 100, 100)
        return 0

    @property
    def weeks_lived(self):
        """Calculate weeks lived since birth"""
        if self.birth_date:
            today = date.today()
            return (today - self.birth_date).days // 7
        return 0

    @property
    def weeks_remaining(self):
        """Calculate weeks remaining until 100 years old"""
        if self.birth_date:
            total_weeks = int(100 * 52.18)  # Approximate weeks in 100 years
            return max(total_weeks - self.weeks_lived, 0)
        return 0


class Project(models.Model):
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('on_hold', 'On Hold'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    deadline = models.DateField(null=True, blank=True, help_text="Leave empty for lifetime project")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='not_started')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', 'deadline', '-created_at']

    def __str__(self):
        return self.title

    @property
    def is_overdue(self):
        if self.deadline and self.status not in ['completed', 'cancelled']:
            return date.today() > self.deadline
        return False

    @property
    def days_remaining(self):
        if self.deadline:
            delta = self.deadline - date.today()
            return delta.days if delta.days >= 0 else 0
        return None

    @property
    def progress_percentage(self):
        """Calculate progress based on time elapsed (if deadline is set)"""
        if self.deadline and self.status != 'not_started':
            total_days = (self.deadline - self.created_at.date()).days
            elapsed_days = (date.today() - self.created_at.date()).days
            if total_days > 0:
                progress = min((elapsed_days / total_days) * 100, 100)
                if self.status == 'completed':
                    return 100
                return progress
        elif self.status == 'completed':
            return 100
        return 0
