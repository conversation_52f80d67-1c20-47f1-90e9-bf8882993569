from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, datetime
import calendar


class ProjectCategory(models.Model):
    name = models.CharField(max_length=50, unique=True)
    color = models.CharField(max_length=7, default='#007bff', help_text='Hex color code')
    icon = models.CharField(max_length=50, default='fas fa-folder', help_text='FontAwesome icon class')
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = 'Project Categories'
        ordering = ['name']

    def __str__(self):
        return self.name


class ProjectTag(models.Model):
    name = models.CharField(max_length=30, unique=True)
    color = models.CharField(max_length=7, default='#6c757d', help_text='Hex color code')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class UserProfile(models.Model):
    TIMEZONE_CHOICES = [
        ('UTC', 'UTC'),
        # North America
        ('US/Eastern', 'US Eastern (New York)'),
        ('US/Central', 'US Central (Chicago)'),
        ('US/Mountain', 'US Mountain (Denver)'),
        ('US/Pacific', 'US Pacific (Los Angeles)'),
        ('America/Toronto', 'Toronto'),
        ('America/Vancouver', 'Vancouver'),
        ('America/Mexico_City', 'Mexico City'),
        # Western Europe
        ('Europe/London', 'London (GMT)'),
        ('Europe/Paris', 'Paris (CET)'),
        ('Europe/Berlin', 'Berlin (CET)'),
        ('Europe/Rome', 'Rome (CET)'),
        ('Europe/Madrid', 'Madrid (CET)'),
        ('Europe/Amsterdam', 'Amsterdam (CET)'),
        ('Europe/Brussels', 'Brussels (CET)'),
        ('Europe/Zurich', 'Zurich (CET)'),
        ('Europe/Vienna', 'Vienna (CET)'),
        ('Europe/Prague', 'Prague (CET)'),
        ('Europe/Warsaw', 'Warsaw (CET)'),
        ('Europe/Stockholm', 'Stockholm (CET)'),
        ('Europe/Oslo', 'Oslo (CET)'),
        ('Europe/Copenhagen', 'Copenhagen (CET)'),
        ('Europe/Helsinki', 'Helsinki (EET)'),
        # Eastern Europe & Balkans
        ('Europe/Sofia', 'Sofia, Bulgaria (EET)'),
        ('Europe/Bucharest', 'Bucharest, Romania (EET)'),
        ('Europe/Athens', 'Athens, Greece (EET)'),
        ('Europe/Kiev', 'Kiev, Ukraine (EET)'),
        ('Europe/Riga', 'Riga, Latvia (EET)'),
        ('Europe/Vilnius', 'Vilnius, Lithuania (EET)'),
        ('Europe/Tallinn', 'Tallinn, Estonia (EET)'),
        ('Europe/Belgrade', 'Belgrade, Serbia (CET)'),
        ('Europe/Zagreb', 'Zagreb, Croatia (CET)'),
        ('Europe/Ljubljana', 'Ljubljana, Slovenia (CET)'),
        ('Europe/Budapest', 'Budapest, Hungary (CET)'),
        ('Europe/Bratislava', 'Bratislava, Slovakia (CET)'),
        # Russia & CIS
        ('Europe/Moscow', 'Moscow (MSK)'),
        ('Europe/Volgograd', 'Volgograd (MSK)'),
        ('Asia/Yekaterinburg', 'Yekaterinburg (YEKT)'),
        # Asia
        ('Asia/Tokyo', 'Tokyo (JST)'),
        ('Asia/Shanghai', 'Shanghai (CST)'),
        ('Asia/Hong_Kong', 'Hong Kong (HKT)'),
        ('Asia/Singapore', 'Singapore (SGT)'),
        ('Asia/Seoul', 'Seoul (KST)'),
        ('Asia/Bangkok', 'Bangkok (ICT)'),
        ('Asia/Dubai', 'Dubai (GST)'),
        ('Asia/Istanbul', 'Istanbul (TRT)'),
        # Australia & Oceania
        ('Australia/Sydney', 'Sydney (AEDT)'),
        ('Australia/Melbourne', 'Melbourne (AEDT)'),
        ('Australia/Perth', 'Perth (AWST)'),
        ('Pacific/Auckland', 'Auckland (NZDT)'),
        # Africa
        ('Africa/Cairo', 'Cairo (EET)'),
        ('Africa/Johannesburg', 'Johannesburg (SAST)'),
        # South America
        ('America/Sao_Paulo', 'São Paulo (BRT)'),
        ('America/Buenos_Aires', 'Buenos Aires (ART)'),
    ]

    THEME_CHOICES = [
        ('light', 'Light Theme'),
        ('dark', 'Dark Theme'),
        ('auto', 'Auto (System)'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    birth_date = models.DateField(null=True, blank=True, help_text="Used to calculate your life timeline")
    timezone = models.CharField(max_length=50, choices=TIMEZONE_CHOICES, default='UTC')
    theme = models.CharField(max_length=10, choices=THEME_CHOICES, default='light')
    life_expectancy = models.IntegerField(default=100, help_text="Expected lifespan in years")
    bio = models.TextField(max_length=500, blank=True, help_text="Tell us about yourself")
    location = models.CharField(max_length=100, blank=True, help_text="Your current location")
    website = models.URLField(blank=True, help_text="Your personal website or social media")
    email_notifications = models.BooleanField(default=True, help_text="Receive email notifications")
    weekly_reminders = models.BooleanField(default=True, help_text="Weekly progress reminders")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Profile"

    @property
    def age(self):
        if self.birth_date:
            today = date.today()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    @property
    def life_progress_percentage(self):
        """Calculate life progress as percentage based on life expectancy"""
        if self.birth_date:
            today = date.today()
            total_days = self.life_expectancy * 365.25  # Approximate days in expected lifespan
            days_lived = (today - self.birth_date).days
            return min((days_lived / total_days) * 100, 100)
        return 0

    @property
    def weeks_lived(self):
        """Calculate weeks lived since birth"""
        if self.birth_date:
            today = date.today()
            return (today - self.birth_date).days // 7
        return 0

    @property
    def weeks_remaining(self):
        """Calculate weeks remaining until life expectancy"""
        if self.birth_date:
            total_weeks = int(self.life_expectancy * 52.18)  # Approximate weeks in expected lifespan
            return max(total_weeks - self.weeks_lived, 0)
        return 0

    @property
    def days_lived(self):
        """Calculate total days lived"""
        if self.birth_date:
            return (date.today() - self.birth_date).days
        return 0

    @property
    def years_remaining(self):
        """Calculate years remaining until life expectancy"""
        if self.birth_date and self.age:
            return max(self.life_expectancy - self.age, 0)
        return self.life_expectancy

    @property
    def years_remaining(self):
        """Calculate years remaining until life expectancy"""
        if self.birth_date:
            return max(self.life_expectancy - self.age, 0)
        return 0

    @property
    def life_stage(self):
        """Determine current life stage"""
        if not self.age:
            return "Unknown"
        elif self.age < 13:
            return "Childhood"
        elif self.age < 20:
            return "Adolescence"
        elif self.age < 30:
            return "Young Adult"
        elif self.age < 50:
            return "Adult"
        elif self.age < 65:
            return "Middle Age"
        else:
            return "Senior"

    def get_analytics_data(self):
        """Get comprehensive analytics data for the user"""
        from django.db.models import Count, Q
        from datetime import datetime, timedelta

        projects = Project.objects.filter(user=self.user)

        # Project statistics
        total_projects = projects.count()
        completed_projects = projects.filter(status='completed').count()
        in_progress_projects = projects.filter(status='in_progress').count()
        overdue_projects = projects.filter(
            deadline__lt=date.today(),
            status__in=['not_started', 'in_progress']
        ).count()

        # Completion rate
        completion_rate = (completed_projects / total_projects * 100) if total_projects > 0 else 0

        # Recent activity (last 30 days)
        thirty_days_ago = datetime.now().date() - timedelta(days=30)
        recent_completions = projects.filter(
            status='completed',
            updated_at__gte=thirty_days_ago
        ).count()

        # Priority distribution
        priority_stats = projects.values('priority').annotate(count=Count('priority'))

        # Status distribution
        status_stats = projects.values('status').annotate(count=Count('status'))

        # Milestone analytics
        total_milestones = sum(p.milestones.count() for p in projects)
        completed_milestones = sum(p.milestones.filter(is_completed=True).count() for p in projects)
        overdue_milestones = 0
        upcoming_milestones = 0

        today = date.today()
        week_from_now = today + timedelta(days=7)

        for project in projects:
            overdue_milestones += project.milestones.filter(
                target_date__lt=today,
                is_completed=False
            ).count()
            upcoming_milestones += project.milestones.filter(
                target_date__gte=today,
                target_date__lte=week_from_now,
                is_completed=False
            ).count()

        milestone_completion_rate = (completed_milestones / max(total_milestones, 1)) * 100

        # Dream analytics
        dreams = DreamItem.objects.filter(user=self.user)
        total_dreams = dreams.count()
        achieved_dreams = dreams.filter(is_achieved=True).count()
        dream_milestones = sum(d.milestones.count() for d in dreams)
        completed_dream_milestones = sum(d.milestones.filter(is_completed=True).count() for d in dreams)

        # Milestone streak calculation
        milestone_streak = self.calculate_milestone_streak()

        # Life progress insights
        days_per_year = 365.25
        years_lived = self.age if self.age else 0
        years_remaining = self.years_remaining if hasattr(self, 'years_remaining') else 0

        return {
            'total_projects': total_projects,
            'completed_projects': completed_projects,
            'in_progress_projects': in_progress_projects,
            'overdue_projects': overdue_projects,
            'completion_rate': round(completion_rate, 1),
            'recent_completions': recent_completions,
            'priority_stats': list(priority_stats),
            'status_stats': list(status_stats),
            'total_milestones': total_milestones,
            'completed_milestones': completed_milestones,
            'overdue_milestones': overdue_milestones,
            'upcoming_milestones': upcoming_milestones,
            'milestone_completion_rate': round(milestone_completion_rate, 1),
            'total_dreams': total_dreams,
            'achieved_dreams': achieved_dreams,
            'dream_milestones': dream_milestones,
            'completed_dream_milestones': completed_dream_milestones,
            'milestone_streak': milestone_streak,
            'years_lived': years_lived,
            'years_remaining': years_remaining,
            'life_progress': round(self.life_progress_percentage, 1),
            'productivity_score': self.calculate_productivity_score(),
        }

    def calculate_productivity_score(self):
        """Calculate a productivity score based on project completion and activity"""
        projects = Project.objects.filter(user=self.user)
        total_projects = projects.count()

        if total_projects == 0:
            return 0

        completed = projects.filter(status='completed').count()
        in_progress = projects.filter(status='in_progress').count()
        overdue = projects.filter(
            deadline__lt=date.today(),
            status__in=['not_started', 'in_progress']
        ).count()

        # Calculate score (0-100)
        completion_score = (completed / total_projects) * 60  # 60% weight for completions
        progress_score = (in_progress / total_projects) * 30  # 30% weight for active projects
        penalty = (overdue / total_projects) * 20  # 20% penalty for overdue

        score = completion_score + progress_score - penalty
        return max(0, min(100, round(score, 1)))

    def calculate_milestone_streak(self):
        """Calculate current milestone completion streak (consecutive days with milestone completions)"""
        from datetime import timedelta

        # Get all completed milestones from projects and dreams
        project_milestones = []
        dream_milestones = []

        projects = Project.objects.filter(user=self.user)
        for project in projects:
            project_milestones.extend(project.milestones.filter(is_completed=True, completed_date__isnull=False))

        dreams = DreamItem.objects.filter(user=self.user)
        for dream in dreams:
            dream_milestones.extend(dream.milestones.filter(is_completed=True, completed_date__isnull=False))

        # Combine and sort by completion date
        all_milestones = project_milestones + dream_milestones
        if not all_milestones:
            return 0

        all_milestones.sort(key=lambda x: x.completed_date, reverse=True)

        # Calculate streak
        streak = 0
        current_date = date.today()

        # Group milestones by completion date
        completion_dates = set()
        for milestone in all_milestones:
            completion_dates.add(milestone.completed_date.date())

        completion_dates = sorted(completion_dates, reverse=True)

        # Check for consecutive days
        for completion_date in completion_dates:
            if completion_date == current_date or completion_date == current_date - timedelta(days=streak):
                streak += 1
                current_date = completion_date
            else:
                break

        return streak


class DreamItem(models.Model):
    CATEGORY_CHOICES = [
        ('travel', 'Travel & Adventure'),
        ('career', 'Career & Business'),
        ('lifestyle', 'Lifestyle & Home'),
        ('relationships', 'Relationships & Family'),
        ('health', 'Health & Fitness'),
        ('education', 'Education & Skills'),
        ('material', 'Material Goals'),
        ('experiences', 'Experiences'),
        ('other', 'Other'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='dream_images/', blank=True, null=True, help_text="Upload an image of your dream")
    image_url = models.URLField(blank=True, help_text="Or provide a URL to an image representing this dream")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='other')
    target_date = models.DateField(null=True, blank=True, help_text="When you want to achieve this")
    priority = models.IntegerField(default=5, help_text="Priority from 1-10")
    is_achieved = models.BooleanField(default=False)
    achieved_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True)

    # Visual properties for the dream wall
    position_x = models.FloatField(default=0, help_text="X position on dream wall (0-100)")
    position_y = models.FloatField(default=0, help_text="Y position on dream wall (0-100)")
    size_factor = models.FloatField(default=1.0, help_text="Size multiplier (0.5-2.0)")
    rotation = models.FloatField(default=0, help_text="Rotation in degrees (-15 to 15)")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', '-created_at']

    def __str__(self):
        return self.title

    def get_image_url(self):
        """Get the image URL (uploaded file takes priority over URL)"""
        if self.image:
            return self.image.url
        elif self.image_url:
            return self.image_url
        return None

    @property
    def image_display_url(self):
        """Template-friendly property for getting image URL"""
        return self.get_image_url()

    def get_thumbnail_url(self, size='medium'):
        """Get optimized thumbnail URL for different display sizes"""
        if self.image:
            return self.image.url
        elif self.image_url:
            # For external URLs, add size parameters if it's from Unsplash
            if 'unsplash.com' in self.image_url:
                if size == 'small':
                    return f"{self.image_url}&w=300&h=300&fit=crop"
                elif size == 'large':
                    return f"{self.image_url}&w=600&h=400&fit=crop"
                else:  # medium
                    return f"{self.image_url}&w=400&h=300&fit=crop"
            return self.image_url
        return None

    def save(self, *args, **kwargs):
        # Auto-generate random position and properties if not set
        if self.position_x == 0 and self.position_y == 0:
            import random
            self.position_x = random.uniform(5, 95)
            self.position_y = random.uniform(5, 95)
            self.size_factor = random.uniform(0.8, 1.5)
            self.rotation = random.uniform(-10, 10)

        # Set achieved date when marked as achieved
        if self.is_achieved and not self.achieved_date:
            self.achieved_date = date.today()
        elif not self.is_achieved:
            self.achieved_date = None

        # Resize image if uploaded
        if self.image:
            self.resize_image()

        super().save(*args, **kwargs)

    def resize_image(self):
        """Resize uploaded image to optimize for web display and reduce file size"""
        try:
            from PIL import Image
            import os

            if not self.image or not os.path.exists(self.image.path):
                return

            # Get original file size
            original_size = os.path.getsize(self.image.path)

            # Open the image
            img = Image.open(self.image.path)
            original_width, original_height = img.size

            # Convert to RGB if necessary (for JPEG compatibility)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparent images
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background

            # Determine target size based on original size and file size
            if original_size > 2 * 1024 * 1024:  # If larger than 2MB
                max_width, max_height = 1200, 800
                quality = 75
            elif original_size > 1 * 1024 * 1024:  # If larger than 1MB
                max_width, max_height = 1000, 700
                quality = 80
            else:
                max_width, max_height = 800, 600
                quality = 85

            # Only resize if image is larger than target
            if original_width > max_width or original_height > max_height:
                img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
                print(f"Resized image from {original_width}x{original_height} to {img.size}")

            # Save the optimized image
            img.save(self.image.path, 'JPEG', quality=quality, optimize=True)

            # Check final file size
            final_size = os.path.getsize(self.image.path)
            print(f"Image optimization: {original_size // 1024}KB -> {final_size // 1024}KB")

        except Exception as e:
            # If resizing fails, log the error but don't break the save
            print(f"Image resize failed for {self.title}: {e}")
            # Try to at least convert to JPEG if it's not already
            try:
                from PIL import Image
                img = Image.open(self.image.path)
                if img.format != 'JPEG':
                    if img.mode in ('RGBA', 'LA', 'P'):
                        img = img.convert('RGB')
                    img.save(self.image.path, 'JPEG', quality=85)
            except:
                pass  # If even this fails, keep original file

    def get_milestone_progress(self):
        """Calculate progress based on completed milestones"""
        milestones = self.milestones.all()
        if milestones.exists():
            total_milestones = milestones.count()
            completed_milestones = milestones.filter(is_completed=True).count()
            return (completed_milestones / total_milestones) * 100
        return None

    @property
    def milestone_stats(self):
        """Get milestone statistics"""
        milestones = self.milestones.all()
        total = milestones.count()
        completed = milestones.filter(is_completed=True).count()
        return {
            'total': total,
            'completed': completed,
            'remaining': total - completed,
            'progress': (completed / total * 100) if total > 0 else 0
        }

    @property
    def next_milestone(self):
        """Get the next milestone to work on"""
        return self.milestones.filter(is_completed=False).first()

    @property
    def recent_milestones(self):
        """Get recently completed milestones (last 7 days)"""
        from datetime import timedelta
        week_ago = date.today() - timedelta(days=7)
        return self.milestones.filter(
            is_completed=True,
            completed_date__gte=week_ago
        ).order_by('-completed_date')

    def get_milestone_suggestions(self):
        """Get smart suggestions for next milestones based on project progress"""
        suggestions = []

        # If no milestones exist, suggest creating some
        if not self.milestones.exists():
            suggestions.append({
                'type': 'create_milestones',
                'title': 'Break down your project',
                'description': 'Add milestones to track your progress effectively',
                'action': 'Add Milestones',
                'priority': 'high'
            })
            return suggestions

        # Check for overdue milestones
        overdue = self.milestones.filter(
            target_date__lt=date.today(),
            is_completed=False
        ).count()

        if overdue > 0:
            suggestions.append({
                'type': 'overdue_milestones',
                'title': f'{overdue} overdue milestone{"s" if overdue > 1 else ""}',
                'description': 'Review and update your overdue milestones',
                'action': 'Review Overdue',
                'priority': 'urgent'
            })

        # Suggest next milestone if available
        next_milestone = self.next_milestone
        if next_milestone:
            days_until = None
            if next_milestone.target_date:
                days_until = (next_milestone.target_date - date.today()).days

            suggestions.append({
                'type': 'next_milestone',
                'title': f'Work on: {next_milestone.title}',
                'description': f'Target: {next_milestone.target_date.strftime("%b %d") if next_milestone.target_date else "No deadline"}',
                'action': 'Start Working',
                'priority': 'medium',
                'days_until': days_until
            })

        # Celebrate if project is nearly complete
        progress = self.get_progress_percentage()
        if progress >= 80 and progress < 100:
            suggestions.append({
                'type': 'almost_complete',
                'title': 'Almost there!',
                'description': f'You\'re {progress:.0f}% complete. Keep pushing!',
                'action': 'Finish Strong',
                'priority': 'low'
            })

        return suggestions

    @property
    def progress_percentage(self):
        """Get progress percentage based on milestones"""
        milestone_progress = self.get_milestone_progress()
        if milestone_progress is not None:
            return milestone_progress

        # If no milestones, return 0 unless achieved
        if self.is_achieved:
            return 100
        return 0


class Project(models.Model):
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('on_hold', 'On Hold'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    category = models.ForeignKey(ProjectCategory, on_delete=models.SET_NULL, null=True, blank=True)
    tags = models.ManyToManyField(ProjectTag, blank=True)
    deadline = models.DateField(null=True, blank=True, help_text="Leave empty for lifetime project")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='not_started')
    progress_percentage = models.IntegerField(default=0, help_text="Manual progress tracking (0-100)")
    notes = models.TextField(blank=True, help_text="Additional notes and updates")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', 'deadline', '-created_at']

    def __str__(self):
        return self.title

    @property
    def is_overdue(self):
        if self.deadline and self.status not in ['completed', 'cancelled']:
            return date.today() > self.deadline
        return False

    @property
    def days_remaining(self):
        if self.deadline:
            delta = self.deadline - date.today()
            return delta.days if delta.days >= 0 else 0
        return None

    def get_calculated_progress(self):
        """Calculate progress based on time elapsed (if deadline is set)"""
        if self.deadline and self.status != 'not_started':
            total_days = (self.deadline - self.created_at.date()).days
            elapsed_days = (date.today() - self.created_at.date()).days
            if total_days > 0:
                progress = min((elapsed_days / total_days) * 100, 100)
                if self.status == 'completed':
                    return 100
                return progress
        elif self.status == 'completed':
            return 100
        return 0

    def get_progress_percentage(self):
        """Get progress percentage based on milestones, manual input, or time calculation"""
        # First priority: milestone-based progress
        milestone_progress = self.get_milestone_progress()
        if milestone_progress is not None:
            return milestone_progress

        # Second priority: manual progress
        if self.progress_percentage > 0:
            return self.progress_percentage

        # Third priority: time-based calculation
        return self.get_calculated_progress()

    def get_milestone_progress(self):
        """Calculate progress based on completed milestones"""
        milestones = self.milestones.all()
        if milestones.exists():
            total_milestones = milestones.count()
            completed_milestones = milestones.filter(is_completed=True).count()
            return (completed_milestones / total_milestones) * 100
        return None

    @property
    def milestone_stats(self):
        """Get milestone statistics"""
        milestones = self.milestones.all()
        total = milestones.count()
        completed = milestones.filter(is_completed=True).count()
        return {
            'total': total,
            'completed': completed,
            'remaining': total - completed,
            'progress': (completed / total * 100) if total > 0 else 0
        }

    @property
    def next_milestone(self):
        """Get the next milestone to work on"""
        return self.milestones.filter(is_completed=False).first()

    @property
    def recent_milestones(self):
        """Get recently completed milestones (last 7 days)"""
        from datetime import timedelta
        week_ago = date.today() - timedelta(days=7)
        return self.milestones.filter(
            is_completed=True,
            completed_date__gte=week_ago
        ).order_by('-completed_date')

    def get_milestone_suggestions(self):
        """Get smart suggestions for next milestones based on project progress"""
        suggestions = []

        # If no milestones exist, suggest creating some
        if not self.milestones.exists():
            suggestions.append({
                'type': 'create_milestones',
                'title': 'Break down your project',
                'description': 'Add milestones to track your progress effectively',
                'action': 'Add Milestones',
                'priority': 'high'
            })
            return suggestions

        # Check for overdue milestones
        overdue = self.milestones.filter(
            target_date__lt=date.today(),
            is_completed=False
        ).count()

        if overdue > 0:
            suggestions.append({
                'type': 'overdue_milestones',
                'title': f'{overdue} overdue milestone{"s" if overdue > 1 else ""}',
                'description': 'Review and update your overdue milestones',
                'action': 'Review Overdue',
                'priority': 'urgent'
            })

        # Suggest next milestone if available
        next_milestone = self.next_milestone
        if next_milestone:
            days_until = None
            if next_milestone.target_date:
                days_until = (next_milestone.target_date - date.today()).days

            suggestions.append({
                'type': 'next_milestone',
                'title': f'Work on: {next_milestone.title}',
                'description': f'Target: {next_milestone.target_date.strftime("%b %d") if next_milestone.target_date else "No deadline"}',
                'action': 'Start Working',
                'priority': 'medium',
                'days_until': days_until
            })

        # Celebrate if project is nearly complete
        progress = self.get_progress_percentage()
        if progress >= 80 and progress < 100:
            suggestions.append({
                'type': 'almost_complete',
                'title': 'Almost there!',
                'description': f'You\'re {progress:.0f}% complete. Keep pushing!',
                'action': 'Finish Strong',
                'priority': 'low'
            })

        return suggestions


class Milestone(models.Model):
    """Milestones for tracking project progress"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='milestones')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    target_date = models.DateField(null=True, blank=True, help_text="When you want to complete this milestone")
    is_completed = models.BooleanField(default=False)
    completed_date = models.DateTimeField(null=True, blank=True)
    order = models.PositiveIntegerField(default=0, help_text="Order of milestone in project")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'target_date', 'created_at']
        unique_together = ['project', 'order']

    def __str__(self):
        status = "✓" if self.is_completed else "○"
        return f"{status} {self.title}"

    def save(self, *args, **kwargs):
        # Set completed_date when marking as completed
        if self.is_completed and not self.completed_date:
            from django.utils import timezone
            self.completed_date = timezone.now()
        elif not self.is_completed:
            self.completed_date = None
        super().save(*args, **kwargs)

    @property
    def is_overdue(self):
        """Check if milestone is overdue"""
        if self.target_date and not self.is_completed:
            return date.today() > self.target_date
        return False

    @property
    def days_remaining(self):
        """Days remaining until target date"""
        if self.target_date and not self.is_completed:
            delta = self.target_date - date.today()
            return delta.days if delta.days >= 0 else 0
        return None


class MilestoneTemplate(models.Model):
    """Predefined milestone templates for common project types"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, choices=[
        ('web_development', 'Web Development'),
        ('mobile_app', 'Mobile App'),
        ('learning', 'Learning/Education'),
        ('fitness', 'Fitness/Health'),
        ('business', 'Business/Startup'),
        ('creative', 'Creative Project'),
        ('travel', 'Travel Planning'),
        ('career', 'Career Development'),
        ('financial', 'Financial Goal'),
        ('general', 'General Project'),
    ])
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"


class NotificationManager(models.Manager):
    """Custom manager for notifications"""

    def active(self):
        """Get non-expired, non-dismissed notifications"""
        from django.utils import timezone
        now = timezone.now()
        return self.filter(
            is_dismissed=False
        ).filter(
            models.Q(expires_at__isnull=True) | models.Q(expires_at__gt=now)
        )

    def for_user(self, user):
        """Get notifications for a specific user"""
        return self.filter(user=user)


class Notification(models.Model):
    """Smart notifications for users"""
    NOTIFICATION_TYPES = [
        ('milestone_overdue', 'Milestone Overdue'),
        ('project_stalled', 'Project Stalled'),
        ('streak_broken', 'Streak Broken'),
        ('achievement_unlocked', 'Achievement Unlocked'),
        ('suggestion', 'Smart Suggestion'),
        ('reminder', 'Reminder'),
        ('celebration', 'Celebration'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')
    title = models.CharField(max_length=200)
    message = models.TextField()
    action_url = models.URLField(blank=True, null=True)
    action_text = models.CharField(max_length=100, blank=True)
    is_read = models.BooleanField(default=False)
    is_dismissed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(blank=True, null=True)

    # Related objects for context
    related_project = models.ForeignKey('Project', on_delete=models.CASCADE, blank=True, null=True)
    related_milestone = models.ForeignKey('Milestone', on_delete=models.CASCADE, blank=True, null=True)
    related_dream = models.ForeignKey('DreamItem', on_delete=models.CASCADE, blank=True, null=True)

    # Custom manager
    objects = NotificationManager()

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.user.username})"

    @property
    def is_expired(self):
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False

    @property
    def priority_icon(self):
        icons = {
            'low': 'fas fa-info-circle',
            'medium': 'fas fa-exclamation-circle',
            'high': 'fas fa-exclamation-triangle',
            'urgent': 'fas fa-fire',
        }
        return icons.get(self.priority, 'fas fa-bell')

    @property
    def priority_color(self):
        colors = {
            'low': 'text-info',
            'medium': 'text-warning',
            'high': 'text-danger',
            'urgent': 'text-danger',
        }
        return colors.get(self.priority, 'text-primary')


class MilestoneTemplateItem(models.Model):
    """Individual milestone items within a template"""
    template = models.ForeignKey(MilestoneTemplate, on_delete=models.CASCADE, related_name='items')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    order = models.PositiveIntegerField()
    days_offset = models.PositiveIntegerField(help_text="Days from project start")
    is_critical = models.BooleanField(default=False, help_text="Critical milestone that affects project success")

    class Meta:
        ordering = ['order']
        unique_together = ['template', 'order']

    def __str__(self):
        return f"{self.template.name} - {self.title}"


class DreamMilestone(models.Model):
    """Milestones for tracking dream progress"""
    dream = models.ForeignKey(DreamItem, on_delete=models.CASCADE, related_name='milestones')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    target_date = models.DateField(null=True, blank=True, help_text="When you want to complete this milestone")
    is_completed = models.BooleanField(default=False)
    completed_date = models.DateTimeField(null=True, blank=True)
    order = models.PositiveIntegerField(default=0, help_text="Order of milestone in dream")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'target_date', 'created_at']
        unique_together = ['dream', 'order']

    def __str__(self):
        status = "✓" if self.is_completed else "○"
        return f"{status} {self.title}"

    def save(self, *args, **kwargs):
        # Set completed_date when marking as completed
        if self.is_completed and not self.completed_date:
            from django.utils import timezone
            self.completed_date = timezone.now()
        elif not self.is_completed:
            self.completed_date = None
        super().save(*args, **kwargs)

    @property
    def is_overdue(self):
        """Check if milestone is overdue"""
        if self.target_date and not self.is_completed:
            return date.today() > self.target_date
        return False

    @property
    def days_remaining(self):
        """Days remaining until target date"""
        if self.target_date and not self.is_completed:
            delta = self.target_date - date.today()
            return delta.days if delta.days >= 0 else 0
        return None
