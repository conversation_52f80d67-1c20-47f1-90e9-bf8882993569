{% extends 'base.html' %}

{% block title %}Dashboard - TimeLine{% endblock %}

{% block content %}
<!-- Current Time Display -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-0">Welcome back, {{ user.first_name|default:user.username }}!</h4>
                        <p class="mb-0">Track your life progress and manage your projects</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div id="current-time" class="h5 mb-0">
                            <i class="fas fa-clock"></i>
                            <span id="time-display">{{ current_time|date:"F d, Y - H:i:s" }}</span>
                            <br>
                            <small id="timezone-info" class="text-light opacity-75">
                                {% if profile.timezone %}{{ profile.timezone }}{% else %}UTC{% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Overview -->
{% if analytics.total_projects > 0 %}
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-primary">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.total_projects }}</h3>
                <p class="analytics-label">Total Projects</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.completion_rate }}%</h3>
                <p class="analytics-label">Completion Rate</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.in_progress_projects }}</h3>
                <p class="analytics-label">In Progress</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-info">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.productivity_score }}</h3>
                <p class="analytics-label">Productivity Score</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Life Timeline Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-heartbeat text-danger"></i> Life Timeline
                </h5>
                {% if profile.birth_date %}
                    <button class="btn btn-sm btn-outline-primary" onclick="window.location.href='{% url 'weeks_view' %}'">
                        <i class="fas fa-calendar-alt"></i> View Weeks
                    </button>
                {% endif %}
            </div>
            <div class="card-body">
                {% if profile.birth_date %}
                    <div class="life-timeline-container">
                        <div class="timeline-info mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Age:</strong> {{ profile.age }} years
                                </div>
                                <div class="col-md-3">
                                    <strong>Progress:</strong> {{ profile.life_progress_percentage|floatformat:1 }}%
                                </div>
                                <div class="col-md-3">
                                    <strong>Weeks Lived:</strong> {{ profile.weeks_lived }}
                                </div>
                                <div class="col-md-3">
                                    <strong>Weeks Remaining:</strong> {{ profile.weeks_remaining }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="life-progress-bar" onclick="window.location.href='{% url 'weeks_view' %}'" style="cursor: pointer;">
                            <div class="progress-container">
                                <div class="progress-fill" style="width: {{ profile.life_progress_percentage }}%;">
                                    <div class="sand-effect"></div>
                                </div>
                                <div class="progress-text">
                                    Life Progress: {{ profile.life_progress_percentage|floatformat:1 }}%
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-markers mt-2">
                            <div class="marker" style="left: 0%;">0</div>
                            <div class="marker" style="left: 25%;">25</div>
                            <div class="marker" style="left: 50%;">50</div>
                            <div class="marker" style="left: 75%;">75</div>
                            <div class="marker" style="left: 100%;">100</div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                        <h6>Set Your Birth Date</h6>
                        <p class="text-muted">Please set your birth date to see your life timeline.</p>
                        <a href="{% url 'profile_edit' %}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Deadlines and Recent Activity -->
{% if upcoming_deadlines or recent_projects %}
<div class="row mb-4">
    {% if upcoming_deadlines %}
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Upcoming Deadlines
                </h6>
            </div>
            <div class="card-body">
                {% for project in upcoming_deadlines %}
                    <div class="deadline-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ project.title }}</strong>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> {{ project.deadline|date:"M d, Y" }}
                                    {% if project.days_remaining <= 3 %}
                                        <span class="badge bg-danger ms-1">{{ project.days_remaining }} days left</span>
                                    {% elif project.days_remaining <= 7 %}
                                        <span class="badge bg-warning ms-1">{{ project.days_remaining }} days left</span>
                                    {% endif %}
                                </small>
                            </div>
                            <span class="badge bg-{{ project.priority }}">{{ project.get_priority_display }}</span>
                        </div>
                    </div>
                    {% if not forloop.last %}<hr class="my-2">{% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    {% if recent_projects %}
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history text-info"></i> Recent Activity
                </h6>
            </div>
            <div class="card-body">
                {% for project in recent_projects %}
                    <div class="activity-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ project.title }}</strong>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-flag"></i> {{ project.get_status_display }}
                                    • Updated {{ project.updated_at|timesince }} ago
                                </small>
                            </div>
                            <div class="progress-circle" data-progress="{{ project.progress_percentage }}">
                                <span>{{ project.progress_percentage|floatformat:0 }}%</span>
                            </div>
                        </div>
                    </div>
                    {% if not forloop.last %}<hr class="my-2">{% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Projects Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-tasks text-success"></i> Your Projects
                </h5>
                <a href="{% url 'project_create' %}" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> Add Project
                </a>
            </div>
            <div class="card-body">
                {% if projects %}
                    <div class="row">
                        {% for project in projects %}
                            <div class="col-md-6 mb-3">
                                <div class="card project-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">{{ project.title }}</h6>
                                            <span class="badge bg-{{ project.priority|default:'secondary' }}">
                                                {{ project.get_priority_display }}
                                            </span>
                                        </div>
                                        
                                        {% if project.description %}
                                            <p class="card-text text-muted small">{{ project.description|truncatewords:15 }}</p>
                                        {% endif %}
                                        
                                        <div class="project-progress mb-2">
                                            <div class="progress">
                                                <div class="progress-bar bg-success" style="width: {{ project.progress_percentage }}%"></div>
                                            </div>
                                            <small class="text-muted">{{ project.progress_percentage|floatformat:0 }}% complete</small>
                                        </div>
                                        
                                        <div class="project-info">
                                            <small class="text-muted">
                                                <i class="fas fa-flag"></i> {{ project.get_status_display }}
                                                {% if project.deadline %}
                                                    | <i class="fas fa-calendar"></i> {{ project.deadline }}
                                                    {% if project.is_overdue %}
                                                        <span class="text-danger">(Overdue)</span>
                                                    {% endif %}
                                                {% else %}
                                                    | <i class="fas fa-infinity"></i> Lifetime project
                                                {% endif %}
                                            </small>
                                        </div>
                                        
                                        <div class="mt-2">
                                            <a href="{% url 'project_edit' project.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="{% url 'project_delete' project.id %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h6>No Projects Yet</h6>
                        <p class="text-muted">Create your first project to start tracking your goals.</p>
                        <a href="{% url 'project_create' %}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Create Project
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update current time every 10 minutes to reduce server load
    function updateTime() {
        fetch('{% url "current_time" %}')
            .then(response => response.json())
            .then(data => {
                document.getElementById('time-display').textContent = data.date + ' - ' + data.time;
                const timezoneInfo = document.getElementById('timezone-info');
                if (timezoneInfo && data.timezone) {
                    // Show timezone with UTC offset
                    const timezoneDisplay = data.timezone + ' (UTC' + data.utc_offset + ')';
                    timezoneInfo.textContent = timezoneDisplay;
                }
            })
            .catch(error => {
                console.error('Error updating time:', error);
                // Fallback to local time
                updateLocalTime();
            });
    }

    // Update time immediately and then every 10 minutes (600,000 ms)
    updateTime();
    setInterval(updateTime, 600000);

    // Fallback to local time if server time fails
    function updateLocalTime() {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'UTC' // Force UTC for consistency
        };

        // Try to use user's preferred timezone if available
        const userTimezone = '{% if profile.timezone %}{{ profile.timezone }}{% endif %}';
        if (userTimezone) {
            options.timeZone = userTimezone;
        }

        const timeString = now.toLocaleDateString('en-US', options);
        document.getElementById('time-display').textContent = timeString;

        // Update timezone info
        const timezoneInfo = document.getElementById('timezone-info');
        if (timezoneInfo) {
            const displayTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
            timezoneInfo.textContent = displayTimezone + ' (Browser)';
        }
    }

    // Update local time every minute
    setInterval(updateLocalTime, 60000);
</script>
{% endblock %}
