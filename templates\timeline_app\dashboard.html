{% extends 'base.html' %}

{% block title %}Dashboard - TimeLine{% endblock %}

{% block content %}
<!-- Current Time Display -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-0">Welcome back, {{ user.first_name|default:user.username }}!</h4>
                        <p class="mb-0">Track your life progress and manage your projects</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div id="current-time" class="h5 mb-0">
                            <i class="fas fa-clock" id="clock-icon"></i>
                            <span id="time-display">Loading...</span>
                            <br>
                            <small id="timezone-info" class="text-light opacity-75">
                                Loading timezone...
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Overview -->
{% if analytics.total_projects > 0 %}
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-primary">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.total_projects }}</h3>
                <p class="analytics-label">Total Projects</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.completion_rate }}%</h3>
                <p class="analytics-label">Completion Rate</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.in_progress_projects }}</h3>
                <p class="analytics-label">In Progress</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-info">
                <i class="fas fa-fire"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.milestone_streak }}</h3>
                <p class="analytics-label">Day Streak</p>
            </div>
        </div>
    </div>
</div>

<!-- Milestone Analytics -->
{% if analytics.total_milestones > 0 %}
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-gradient-primary">
                <i class="fas fa-flag-checkered"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.total_milestones }}</h3>
                <p class="analytics-label">Total Milestones</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-gradient-success">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.milestone_completion_rate|floatformat:0 }}%</h3>
                <p class="analytics-label">Milestone Success</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-gradient-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.overdue_milestones }}</h3>
                <p class="analytics-label">Overdue</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="analytics-card">
            <div class="analytics-icon bg-gradient-warning">
                <i class="fas fa-calendar-week"></i>
            </div>
            <div class="analytics-content">
                <h3 class="analytics-number">{{ analytics.upcoming_milestones }}</h3>
                <p class="analytics-label">Due This Week</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endif %}

<!-- Quick Templates -->
{% if popular_templates %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-magic text-info"></i> Quick Start Templates
                </h6>
                <small class="text-muted">Apply to any project</small>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for template in popular_templates %}
                        <div class="col-md-4 col-lg-2 mb-3">
                            <div class="template-quick-card" onclick="selectQuickTemplate({{ template.id }})">
                                <div class="template-icon">
                                    {% if template.category == 'business' %}
                                        <i class="fas fa-briefcase"></i>
                                    {% elif template.category == 'personal' %}
                                        <i class="fas fa-user"></i>
                                    {% elif template.category == 'health' %}
                                        <i class="fas fa-heart"></i>
                                    {% elif template.category == 'education' %}
                                        <i class="fas fa-graduation-cap"></i>
                                    {% elif template.category == 'creative' %}
                                        <i class="fas fa-palette"></i>
                                    {% else %}
                                        <i class="fas fa-star"></i>
                                    {% endif %}
                                </div>
                                <div class="template-name">{{ template.name }}</div>
                                <div class="template-count">{{ template.items.count }} steps</div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Smart Notifications -->
{% if recent_notifications %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-bell text-primary"></i> Smart Notifications
                </h6>
                <a href="{% url 'notifications' %}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                {% for notification in recent_notifications %}
                    <div class="notification-item" data-notification-id="{{ notification.id }}">
                        <div class="notification-icon">
                            <i class="{{ notification.priority_icon }} {{ notification.priority_color }}"></i>
                        </div>
                        <div class="notification-content">
                            <h6 class="notification-title">{{ notification.title }}</h6>
                            <p class="notification-message">{{ notification.message }}</p>
                            <div class="notification-actions">
                                {% if notification.action_url %}
                                    <a href="{{ notification.action_url }}" class="btn btn-sm btn-primary">
                                        {{ notification.action_text|default:"View" }}
                                    </a>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-secondary" onclick="dismissNotification({{ notification.id }})">
                                    Dismiss
                                </button>
                            </div>
                            <small class="text-muted">{{ notification.created_at|timesince }} ago</small>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Milestone Alerts -->
{% if analytics.overdue_milestones > 0 or analytics.upcoming_milestones > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-bell"></i> Milestone Alerts
                </h6>
            </div>
            <div class="card-body">
                {% if analytics.overdue_milestones > 0 %}
                    <div class="alert alert-danger d-flex align-items-center mb-3">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <strong>{{ analytics.overdue_milestones }} Overdue Milestone{{ analytics.overdue_milestones|pluralize }}</strong>
                            <br>
                            <small>You have milestones that are past their target date. Review and update your progress.</small>
                        </div>
                    </div>
                {% endif %}

                {% if analytics.upcoming_milestones > 0 %}
                    <div class="alert alert-warning d-flex align-items-center mb-0">
                        <i class="fas fa-calendar-week fa-2x me-3"></i>
                        <div>
                            <strong>{{ analytics.upcoming_milestones }} Milestone{{ analytics.upcoming_milestones|pluralize }} Due This Week</strong>
                            <br>
                            <small>Stay on track with your upcoming deadlines.</small>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Milestone Insights -->
{% if analytics.total_milestones > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Milestone Insights
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="milestone-insights">
                            <div class="insight-item">
                                <div class="insight-icon bg-success">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="insight-content">
                                    <h6>Completion Rate</h6>
                                    <p>{{ analytics.milestone_completion_rate|floatformat:1 }}% of milestones completed</p>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-success" style="width: {{ analytics.milestone_completion_rate }}%"></div>
                                    </div>
                                </div>
                            </div>

                            {% if analytics.milestone_streak > 0 %}
                            <div class="insight-item">
                                <div class="insight-icon bg-warning">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="insight-content">
                                    <h6>Current Streak</h6>
                                    <p>{{ analytics.milestone_streak }} day{{ analytics.milestone_streak|pluralize }} of milestone completions</p>
                                    <small class="text-muted">Keep the momentum going! 🔥</small>
                                </div>
                            </div>
                            {% endif %}

                            {% if analytics.upcoming_milestones > 0 %}
                            <div class="insight-item">
                                <div class="insight-icon bg-info">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="insight-content">
                                    <h6>This Week's Focus</h6>
                                    <p>{{ analytics.upcoming_milestones }} milestone{{ analytics.upcoming_milestones|pluralize }} due this week</p>
                                    <small class="text-muted">Stay focused on your upcoming deadlines</small>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="milestone-donut-chart">
                            <canvas id="milestoneChart" width="150" height="150"></canvas>
                            <div class="chart-center">
                                <div class="chart-percentage">{{ analytics.milestone_completion_rate|floatformat:0 }}%</div>
                                <div class="chart-label">Complete</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Life Timeline Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-heartbeat text-danger"></i> Life Timeline
                </h5>
                {% if profile.birth_date %}
                    <button class="btn btn-sm btn-outline-primary" onclick="window.location.href='{% url 'weeks_view' %}'">
                        <i class="fas fa-calendar-alt"></i> View Weeks
                    </button>
                {% endif %}
            </div>
            <div class="card-body">
                {% if profile.birth_date %}
                    <div class="life-timeline-container">
                        <div class="timeline-info mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Age:</strong> {{ profile.age }} years
                                </div>
                                <div class="col-md-3">
                                    <strong>Progress:</strong> {{ profile.life_progress_percentage|floatformat:1 }}%
                                </div>
                                <div class="col-md-3">
                                    <strong>Weeks Lived:</strong> {{ profile.weeks_lived }}
                                </div>
                                <div class="col-md-3">
                                    <strong>Weeks Remaining:</strong> {{ profile.weeks_remaining }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="life-progress-bar" onclick="window.location.href='{% url 'weeks_view' %}'" style="cursor: pointer;">
                            <div class="progress-container">
                                <div class="progress-fill" style="width: {{ profile.life_progress_percentage }}%;">
                                    <div class="sand-effect"></div>
                                </div>
                                <div class="progress-text">
                                    Life Progress: {{ profile.life_progress_percentage|floatformat:1 }}%
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-markers mt-2">
                            <div class="marker" style="left: 0%;">0</div>
                            <div class="marker" style="left: 25%;">25</div>
                            <div class="marker" style="left: 50%;">50</div>
                            <div class="marker" style="left: 75%;">75</div>
                            <div class="marker" style="left: 100%;">100</div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                        <h6>Set Your Birth Date</h6>
                        <p class="text-muted">Please set your birth date to see your life timeline.</p>
                        <a href="{% url 'profile_edit' %}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Deadlines and Recent Activity -->
{% if upcoming_deadlines or recent_projects %}
<div class="row mb-4">
    {% if upcoming_deadlines %}
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Upcoming Deadlines
                </h6>
            </div>
            <div class="card-body">
                {% for project in upcoming_deadlines %}
                    <div class="deadline-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ project.title }}</strong>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> {{ project.deadline|date:"M d, Y" }}
                                    {% if project.days_remaining <= 3 %}
                                        <span class="badge bg-danger ms-1">{{ project.days_remaining }} days left</span>
                                    {% elif project.days_remaining <= 7 %}
                                        <span class="badge bg-warning ms-1">{{ project.days_remaining }} days left</span>
                                    {% endif %}
                                </small>
                            </div>
                            <span class="badge bg-{{ project.priority }}">{{ project.get_priority_display }}</span>
                        </div>
                    </div>
                    {% if not forloop.last %}<hr class="my-2">{% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    {% if recent_projects %}
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history text-info"></i> Recent Activity
                </h6>
            </div>
            <div class="card-body">
                {% for project in recent_projects %}
                    <div class="activity-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ project.title }}</strong>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-flag"></i> {{ project.get_status_display }}
                                    • Updated {{ project.updated_at|timesince }} ago
                                </small>
                            </div>
                            <div class="progress-circle" data-progress="{{ project.get_progress_percentage }}">
                                <span>{{ project.get_progress_percentage|floatformat:0 }}%</span>
                            </div>
                        </div>
                    </div>
                    {% if not forloop.last %}<hr class="my-2">{% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Projects Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-tasks text-success"></i> Your Projects
                </h5>
                <a href="{% url 'project_create' %}" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> Add Project
                </a>
            </div>
            <div class="card-body">
                {% if projects %}
                    <div class="row">
                        {% for project in projects %}
                            <div class="col-md-6 mb-3">
                                <div class="card project-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">{{ project.title }}</h6>
                                            <span class="badge bg-{{ project.priority|default:'secondary' }}">
                                                {{ project.get_priority_display }}
                                            </span>
                                        </div>
                                        
                                        {% if project.description %}
                                            <p class="card-text text-muted small">{{ project.description|truncatewords:15 }}</p>
                                        {% endif %}
                                        
                                        <div class="project-progress mb-2">
                                            <div class="progress">
                                                <div class="progress-bar bg-{% if project.get_progress_percentage >= 80 %}success{% elif project.get_progress_percentage >= 50 %}info{% elif project.get_progress_percentage >= 25 %}warning{% else %}danger{% endif %}"
                                                     style="width: {{ project.get_progress_percentage }}%"></div>
                                            </div>
                                            <small class="text-muted">
                                                {{ project.get_progress_percentage|floatformat:0 }}% complete
                                                {% if project.milestone_stats.total > 0 %}
                                                    ({{ project.milestone_stats.completed }}/{{ project.milestone_stats.total }} milestones)
                                                {% endif %}
                                            </small>
                                        </div>
                                        
                                        <div class="project-info">
                                            <small class="text-muted">
                                                <i class="fas fa-flag"></i> {{ project.get_status_display }}
                                                {% if project.deadline %}
                                                    | <i class="fas fa-calendar"></i> {{ project.deadline }}
                                                    {% if project.is_overdue %}
                                                        <span class="text-danger">(Overdue)</span>
                                                    {% endif %}
                                                {% else %}
                                                    | <i class="fas fa-infinity"></i> Lifetime project
                                                {% endif %}
                                            </small>
                                        </div>
                                        
                                        <div class="mt-2">
                                            <a href="{% url 'project_detail' project.id %}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="{% url 'project_edit' project.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="{% url 'project_delete' project.id %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h6>No Projects Yet</h6>
                        <p class="text-muted">Create your first project to start tracking your goals.</p>
                        <a href="{% url 'project_create' %}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Create Project
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Time update system - prioritize server time with user's timezone
    let useServerTime = true;

    function updateTime() {
        // Add subtle animation to clock icon
        const clockIcon = document.getElementById('clock-icon');
        if (clockIcon) {
            clockIcon.style.transform = 'scale(1.1)';
            setTimeout(() => {
                clockIcon.style.transform = 'scale(1)';
            }, 200);
        }

        if (useServerTime) {
            // Try to get server time with user's timezone
            fetch('{% url "current_time" %}')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('time-display').textContent = data.date + ' - ' + data.time;
                    const timezoneInfo = document.getElementById('timezone-info');
                    if (timezoneInfo && data.timezone) {
                        // Show timezone with UTC offset
                        const timezoneDisplay = data.timezone + ' (UTC' + data.utc_offset + ')';
                        timezoneInfo.textContent = timezoneDisplay;
                    }
                })
                .catch(error => {
                    console.error('Error updating server time:', error);
                    // Fall back to client-side time
                    useServerTime = false;
                    updateClientTime();
                });
        } else {
            updateClientTime();
        }
    }

    function updateClientTime() {
        const now = new Date();
        const userTimezone = '{% if profile.timezone %}{{ profile.timezone }}{% endif %}';

        // Format options for displaying time
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };

        // Use user's preferred timezone if available
        if (userTimezone) {
            options.timeZone = userTimezone;
        }

        try {
            const formatter = new Intl.DateTimeFormat('en-US', options);
            const timeString = formatter.format(now);
            document.getElementById('time-display').textContent = timeString;

            // Update timezone info
            const timezoneInfo = document.getElementById('timezone-info');
            if (timezoneInfo) {
                const displayTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
                timezoneInfo.textContent = displayTimezone + ' (Client)';
            }
        } catch (error) {
            console.error('Error formatting client time:', error);
            // Ultimate fallback
            document.getElementById('time-display').textContent = now.toLocaleString();
            document.getElementById('timezone-info').textContent = 'Local Time';
        }
    }

    // Update time immediately and then every minute for real-time updates
    updateTime();
    setInterval(updateTime, 60000); // Update every minute

    // Refresh server time every 10 minutes to stay in sync
    setInterval(() => {
        if (!useServerTime) {
            useServerTime = true; // Try server time again
        }
    }, 600000);

// Enhanced progress circle animation
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress circles
    const progressCircles = document.querySelectorAll('.progress-circle');
    progressCircles.forEach(circle => {
        const progress = parseInt(circle.getAttribute('data-progress'));
        const span = circle.querySelector('span');

        // Create SVG circle
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '50');
        svg.setAttribute('height', '50');
        svg.style.position = 'absolute';
        svg.style.top = '0';
        svg.style.left = '0';

        const circleBackground = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circleBackground.setAttribute('cx', '25');
        circleBackground.setAttribute('cy', '25');
        circleBackground.setAttribute('r', '20');
        circleBackground.setAttribute('fill', 'none');
        circleBackground.setAttribute('stroke', '#e9ecef');
        circleBackground.setAttribute('stroke-width', '4');

        const circleProgress = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circleProgress.setAttribute('cx', '25');
        circleProgress.setAttribute('cy', '25');
        circleProgress.setAttribute('r', '20');
        circleProgress.setAttribute('fill', 'none');
        circleProgress.setAttribute('stroke-width', '4');
        circleProgress.setAttribute('stroke-linecap', 'round');
        circleProgress.style.transform = 'rotate(-90deg)';
        circleProgress.style.transformOrigin = '25px 25px';

        // Set color based on progress
        let strokeColor = '#dc3545'; // Red for low progress
        if (progress >= 80) strokeColor = '#28a745'; // Green for high progress
        else if (progress >= 50) strokeColor = '#17a2b8'; // Blue for medium progress
        else if (progress >= 25) strokeColor = '#ffc107'; // Yellow for low-medium progress

        circleProgress.setAttribute('stroke', strokeColor);

        const circumference = 2 * Math.PI * 20;
        circleProgress.setAttribute('stroke-dasharray', circumference);
        circleProgress.setAttribute('stroke-dashoffset', circumference);

        svg.appendChild(circleBackground);
        svg.appendChild(circleProgress);
        circle.appendChild(svg);

        // Animate the circle
        setTimeout(() => {
            const offset = circumference - (progress / 100) * circumference;
            circleProgress.style.transition = 'stroke-dashoffset 1s ease-in-out';
            circleProgress.setAttribute('stroke-dashoffset', offset);
        }, 100);

        // Animate the number
        let currentProgress = 0;
        const increment = progress / 50; // 50 steps for smooth animation
        const timer = setInterval(() => {
            currentProgress += increment;
            if (currentProgress >= progress) {
                currentProgress = progress;
                clearInterval(timer);
            }
            span.textContent = Math.round(currentProgress) + '%';
        }, 20);
    });

    // Add hover effects to analytics cards
    const analyticsCards = document.querySelectorAll('.analytics-card');
    analyticsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        });
    });

    // Add pulse animation to milestone alerts
    const alertElements = document.querySelectorAll('.alert-danger, .alert-warning');
    alertElements.forEach(alert => {
        if (alert.textContent.includes('Overdue')) {
            alert.style.animation = 'pulse 2s infinite';
        }
    });
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .progress-circle {
        position: relative;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.8rem;
    }

    .analytics-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .milestone-alert-enter {
        animation: slideInDown 0.5s ease-out;
    }

    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Enhanced dark theme support for progress circles */
    [data-theme="dark"] .progress-circle svg circle:first-child {
        stroke: #495057;
    }

    /* Smooth theme transitions */
    * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }

    /* Milestone Insights Widget */
    .milestone-insights {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        padding: 0.5rem;
    }

    .insight-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
        background: rgba(255,255,255,0.8);
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .insight-item:hover {
        background: rgba(255,255,255,0.95);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .insight-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .insight-content {
        flex-grow: 1;
        min-width: 0;
    }

    .insight-content h6 {
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #2c3e50;
        font-size: 1rem;
    }

    .insight-content p {
        margin-bottom: 0.5rem;
        color: #5a6c7d;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .insight-content small {
        color: #7f8c8d;
        font-size: 0.8rem;
    }

    .milestone-donut-chart {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(255,255,255,0.8);
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        height: 100%;
        min-height: 200px;
    }

    .chart-center {
        position: absolute;
        text-align: center;
        pointer-events: none;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .chart-percentage {
        font-size: 2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 0.25rem;
    }

    .chart-label {
        font-size: 0.9rem;
        color: #7f8c8d;
        font-weight: 500;
    }

    /* Dark theme support for insights */
    [data-theme="dark"] .insight-item {
        background: rgba(52, 58, 64, 0.8);
        border-color: rgba(255,255,255,0.1);
    }

    [data-theme="dark"] .insight-item:hover {
        background: rgba(52, 58, 64, 0.95);
    }

    [data-theme="dark"] .insight-content h6 {
        color: #f8f9fa;
    }

    [data-theme="dark"] .insight-content p {
        color: #adb5bd;
    }

    [data-theme="dark"] .insight-content small {
        color: #6c757d;
    }

    [data-theme="dark"] .milestone-donut-chart {
        background: rgba(52, 58, 64, 0.8);
        border-color: rgba(255,255,255,0.1);
    }

    [data-theme="dark"] .chart-percentage {
        color: #f8f9fa;
    }

    [data-theme="dark"] .chart-label {
        color: #adb5bd;
    }

    /* Quick Templates */
    .template-quick-card {
        text-align: center;
        padding: 1rem;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .template-quick-card:hover {
        border-color: #007bff;
        background: #f0f8ff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    }

    .template-icon {
        font-size: 1.5rem;
        color: #007bff;
        margin-bottom: 0.5rem;
    }

    .template-name {
        font-weight: 600;
        font-size: 0.85rem;
        color: #2c3e50;
        margin-bottom: 0.25rem;
    }

    .template-count {
        font-size: 0.75rem;
        color: #6c757d;
    }

    /* Dark theme support for templates */
    [data-theme="dark"] .template-quick-card {
        background: #212529;
        border-color: #495057;
    }

    [data-theme="dark"] .template-quick-card:hover {
        background: #2c3e50;
        border-color: #4c6ef5;
    }

    [data-theme="dark"] .template-name {
        color: #f8f9fa;
    }

    /* Smart Notifications */
    .notification-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 1rem;
        background: white;
        transition: all 0.3s ease;
    }

    .notification-item:last-child {
        margin-bottom: 0;
    }

    .notification-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .notification-content {
        flex-grow: 1;
    }

    .notification-title {
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .notification-message {
        margin-bottom: 0.75rem;
        color: #5a6c7d;
        line-height: 1.4;
    }

    .notification-actions {
        margin-bottom: 0.5rem;
    }

    .notification-actions .btn {
        margin-right: 0.5rem;
    }

    /* Dark theme support for notifications */
    [data-theme="dark"] .notification-item {
        background: #212529;
        border-color: #495057;
    }

    [data-theme="dark"] .notification-icon {
        background: #343a40;
    }

    [data-theme="dark"] .notification-title {
        color: #f8f9fa;
    }

    [data-theme="dark"] .notification-message {
        color: #adb5bd;
    }

    /* Clock icon animation */
    #clock-icon {
        transition: transform 0.2s ease;
    }
`;
document.head.appendChild(style);

// Create milestone donut chart
function createMilestoneChart() {
    const canvas = document.getElementById('milestoneChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 50;
    const lineWidth = 12;

    // Get milestone data
    const completed = {{ analytics.completed_milestones }};
    const total = {{ analytics.total_milestones }};
    const percentage = total > 0 ? (completed / total) : 0;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#e9ecef';
    ctx.lineWidth = lineWidth;
    ctx.stroke();

    // Draw progress arc
    if (percentage > 0) {
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, -Math.PI / 2, (-Math.PI / 2) + (2 * Math.PI * percentage));
        ctx.strokeStyle = '#28a745';
        ctx.lineWidth = lineWidth;
        ctx.lineCap = 'round';
        ctx.stroke();
    }
}

// Initialize chart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    createMilestoneChart();
});

// Quick template selection
function selectQuickTemplate(templateId) {
    // Show modal to select project
    const projects = [
        {% for project in recent_projects %}
            {id: {{ project.id }}, title: "{{ project.title|escapejs }}"},
        {% endfor %}
    ];

    if (projects.length === 0) {
        alert('Please create a project first before applying templates.');
        window.location.href = '{% url "project_create" %}';
        return;
    }

    let projectOptions = projects.map(p => `<option value="${p.id}">${p.title}</option>`).join('');

    const modalHtml = `
        <div class="modal fade" id="templateModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Apply Template to Project</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <label class="form-label">Select Project:</label>
                        <select class="form-select" id="projectSelect">
                            ${projectOptions}
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="applyTemplateToProject(${templateId})">Apply Template</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('templateModal'));
    modal.show();

    // Clean up modal when hidden
    document.getElementById('templateModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function applyTemplateToProject(templateId) {
    const projectId = document.getElementById('projectSelect').value;
    window.location.href = `/projects/${projectId}/templates/${templateId}/apply/`;
}

// Notification management
function dismissNotification(notificationId) {
    fetch(`/notifications/${notificationId}/dismiss/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Remove notification from DOM with animation
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.style.transition = 'all 0.3s ease';
                notificationElement.style.opacity = '0';
                notificationElement.style.transform = 'translateX(100%)';

                setTimeout(() => {
                    notificationElement.remove();

                    // Check if no notifications left
                    const remainingNotifications = document.querySelectorAll('.notification-item');
                    if (remainingNotifications.length === 0) {
                        const notificationCard = document.querySelector('.card:has(.notification-item)');
                        if (notificationCard) {
                            notificationCard.style.transition = 'all 0.3s ease';
                            notificationCard.style.opacity = '0';
                            setTimeout(() => notificationCard.remove(), 300);
                        }
                    }
                }, 300);
            }
        }
    })
    .catch(error => {
        console.error('Error dismissing notification:', error);
    });
}
</script>
{% endblock %}
