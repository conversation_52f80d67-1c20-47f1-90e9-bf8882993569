{% extends 'base.html' %}

{% block title %}Life Timeline - TimeLine{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="hero-section text-center py-4">
            <h1 class="display-5 mb-3">
                <i class="fas fa-road text-primary"></i> Your Life Timeline
            </h1>
            <p class="lead">Journey through your life with projects mapped to each year</p>
        </div>
    </div>
</div>

<!-- Timeline Container -->
<div class="timeline-container">
    {% for event in life_events %}
    <div class="timeline-item {% if event.is_current %}current-year{% endif %}">
        <div class="timeline-marker">
            <div class="timeline-year">{{ event.year }}</div>
            <div class="timeline-age">Age {{ event.age }}</div>
        </div>
        
        <div class="timeline-content">
            <div class="timeline-header">
                <h4>{{ event.year }} - Age {{ event.age }}</h4>
                {% if event.is_current %}
                    <span class="badge bg-primary">Current Year</span>
                {% endif %}
            </div>
            
            {% if event.projects %}
            <div class="timeline-projects">
                <h6><i class="fas fa-tasks"></i> Projects ({{ event.projects|length }})</h6>
                <div class="row">
                    {% for project in event.projects %}
                    <div class="col-md-6 mb-2">
                        <div class="timeline-project-card status-{{ project.status }}">
                            <div class="project-info">
                                <strong>{{ project.title }}</strong>
                                <span class="badge bg-{{ project.status }} ms-2">{{ project.get_status_display }}</span>
                            </div>
                            {% if project.description %}
                            <p class="project-desc">{{ project.description|truncatewords:10 }}</p>
                            {% endif %}
                            <div class="project-meta">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> {{ project.created_at|date:"M d" }}
                                    {% if project.deadline %}
                                        • <i class="fas fa-flag"></i> Due {{ project.deadline|date:"M d" }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="no-projects">
                <p class="text-muted"><i class="fas fa-info-circle"></i> No projects recorded for this year</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>

<!-- Life Insights -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Life Insights</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="insight-stat">
                            <h3 class="text-primary">{{ profile.age }}</h3>
                            <p>Years Lived</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="insight-stat">
                            <h3 class="text-success">{{ projects|length }}</h3>
                            <p>Total Projects</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="insight-stat">
                            <h3 class="text-warning">{{ profile.life_progress_percentage|floatformat:1 }}%</h3>
                            <p>Life Progress</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate timeline items
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, { threshold: 0.1 });
    
    timelineItems.forEach(item => {
        observer.observe(item);
    });
    
    // Highlight current year
    const currentYear = document.querySelector('.current-year');
    if (currentYear) {
        currentYear.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
});
</script>
{% endblock %}
