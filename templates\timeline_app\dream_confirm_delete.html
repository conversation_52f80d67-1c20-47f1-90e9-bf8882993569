{% extends 'base.html' %}

{% block title %}Delete Dream - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0 text-danger">
                    <i class="fas fa-exclamation-triangle"></i> Delete Dream
                </h4>
            </div>
            <div class="card-body text-center">
                <div class="delete-warning mb-4">
                    <i class="fas fa-trash fa-3x text-danger mb-3"></i>
                    <h5>Are you sure?</h5>
                    <p class="lead">This action cannot be undone.</p>
                </div>
                
                <div class="dream-preview mb-4">
                    {% if dream.image_url %}
                    <div class="dream-image mb-3">
                        <img src="{{ dream.image_url }}" alt="{{ dream.title }}" 
                             style="max-width: 200px; max-height: 150px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); filter: grayscale(50%);">
                    </div>
                    {% endif %}
                    
                    <h4 class="text-primary">{{ dream.title }}</h4>
                    {% if dream.description %}
                    <p class="text-muted">{{ dream.description|truncatewords:20 }}</p>
                    {% endif %}
                    
                    <div class="dream-meta">
                        <span class="badge bg-{{ dream.category }} me-2">{{ dream.get_category_display }}</span>
                        <span class="badge bg-info">Priority: {{ dream.priority }}/10</span>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="form-actions">
                        <button type="submit" class="btn btn-danger btn-lg me-3">
                            <i class="fas fa-trash"></i> Yes, Delete This Dream
                        </button>
                        <a href="{% url 'dream_wall' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add warning animation
    const warning = document.querySelector('.delete-warning');
    warning.style.opacity = '0';
    warning.style.transform = 'scale(0.8)';
    
    setTimeout(() => {
        warning.style.transition = 'all 0.6s ease';
        warning.style.opacity = '1';
        warning.style.transform = 'scale(1)';
    }, 200);
    
    // Animate dream preview with fade effect
    const preview = document.querySelector('.dream-preview');
    preview.style.opacity = '0';
    preview.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        preview.style.transition = 'all 0.6s ease';
        preview.style.opacity = '0.7';
        preview.style.transform = 'translateY(0)';
    }, 400);
});
</script>
{% endblock %}
