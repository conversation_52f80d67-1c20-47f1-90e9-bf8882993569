# Generated by Django 4.2.7 on 2025-07-11 17:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('timeline_app', '0007_milestonetemplate_milestonetemplateitem'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('milestone_overdue', 'Milestone Overdue'), ('project_stalled', 'Project Stalled'), ('streak_broken', 'Streak Broken'), ('achievement_unlocked', 'Achievement Unlocked'), ('suggestion', 'Smart Suggestion'), ('reminder', 'Reminder'), ('celebration', 'Celebration')], max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('action_url', models.URLField(blank=True, null=True)),
                ('action_text', models.CharField(blank=True, max_length=100)),
                ('is_read', models.BooleanField(default=False)),
                ('is_dismissed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('related_dream', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='timeline_app.dreamitem')),
                ('related_milestone', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='timeline_app.milestone')),
                ('related_project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='timeline_app.project')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
