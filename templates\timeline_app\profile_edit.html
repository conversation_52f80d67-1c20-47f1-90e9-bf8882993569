{% extends 'base.html' %}

{% block title %}Profile Settings - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <!-- Profile Header -->
        <div class="profile-header-card mb-4">
            <div class="profile-header-content">
                <div class="profile-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="profile-info">
                    <h2 class="profile-name">
                        {% if user.first_name or user.last_name %}
                            {{ user.first_name }} {{ user.last_name }}
                        {% else %}
                            {{ user.username }}
                        {% endif %}
                    </h2>
                    <p class="profile-username">@{{ user.username }}</p>
                    {% if profile.life_stage %}
                        <span class="life-stage-badge">{{ profile.life_stage }}</span>
                    {% endif %}
                </div>
                <div class="profile-stats">
                    {% if profile.age %}
                        <div class="stat-item">
                            <div class="stat-number">{{ profile.age }}</div>
                            <div class="stat-label">Years Old</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ profile.life_progress_percentage|floatformat:1 }}%</div>
                            <div class="stat-label">Life Progress</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ profile.years_remaining }}</div>
                            <div class="stat-label">Years Left</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Settings Form -->
        <div class="settings-container">
            <form method="post" class="settings-form">
                {% csrf_token %}

                <!-- Personal Information Section -->
                <div class="settings-section">
                    <div class="section-header">
                        <h5 class="section-title">
                            <i class="fas fa-user text-primary"></i>
                            Personal Information
                        </h5>
                        <p class="section-description">Basic information about yourself</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    {{ form.first_name.label }}
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="form-error">
                                        {% for error in form.first_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    {{ form.last_name.label }}
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="form-error">
                                        {% for error in form.last_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" value="{{ user.username }}" readonly>
                                <small class="form-text text-muted">Username cannot be changed</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="form-error">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.location.id_for_label }}" class="form-label">
                                    {{ form.location.label }}
                                </label>
                                {{ form.location }}
                                {% if form.location.errors %}
                                    <div class="form-error">
                                        {% for error in form.location.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.website.id_for_label }}" class="form-label">
                                    {{ form.website.label }}
                                </label>
                                {{ form.website }}
                                {% if form.website.errors %}
                                    <div class="form-error">
                                        {% for error in form.website.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.bio.id_for_label }}" class="form-label">
                            {{ form.bio.label }}
                        </label>
                        {{ form.bio }}
                        {% if form.bio.errors %}
                            <div class="form-error">
                                {% for error in form.bio.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if form.bio.help_text %}
                            <small class="form-text text-muted">{{ form.bio.help_text }}</small>
                        {% endif %}
                    </div>
                </div>

                <!-- Timeline Settings Section -->
                <div class="settings-section">
                    <div class="section-header">
                        <h5 class="section-title">
                            <i class="fas fa-clock text-success"></i>
                            Timeline Settings
                        </h5>
                        <p class="section-description">Configure your life timeline calculations</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.birth_date.id_for_label }}" class="form-label">
                                    {{ form.birth_date.label }}
                                </label>
                                {{ form.birth_date }}
                                {% if form.birth_date.errors %}
                                    <div class="form-error">
                                        {% for error in form.birth_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.birth_date.help_text %}
                                    <small class="form-text text-muted">{{ form.birth_date.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.life_expectancy.id_for_label }}" class="form-label">
                                    {{ form.life_expectancy.label }}
                                </label>
                                {{ form.life_expectancy }}
                                {% if form.life_expectancy.errors %}
                                    <div class="form-error">
                                        {% for error in form.life_expectancy.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.life_expectancy.help_text %}
                                    <small class="form-text text-muted">{{ form.life_expectancy.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                    
                <!-- Preferences Section -->
                <div class="settings-section">
                    <div class="section-header">
                        <h5 class="section-title">
                            <i class="fas fa-cog text-warning"></i>
                            Preferences
                        </h5>
                        <p class="section-description">Customize your TimeLine experience</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.timezone.id_for_label }}" class="form-label">
                                    {{ form.timezone.label }}
                                </label>
                                {{ form.timezone }}
                                {% if form.timezone.errors %}
                                    <div class="form-error">
                                        {% for error in form.timezone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.theme.id_for_label }}" class="form-label">
                                    {{ form.theme.label }}
                                </label>
                                {{ form.theme }}
                                {% if form.theme.errors %}
                                    <div class="form-error">
                                        {% for error in form.theme.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Section -->
                <div class="settings-section">
                    <div class="section-header">
                        <h5 class="section-title">
                            <i class="fas fa-bell text-info"></i>
                            Notifications
                        </h5>
                        <p class="section-description">Manage your notification preferences</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check-container">
                                {{ form.email_notifications }}
                                <label for="{{ form.email_notifications.id_for_label }}" class="form-check-label">
                                    {{ form.email_notifications.label }}
                                </label>
                                {% if form.email_notifications.help_text %}
                                    <small class="form-text text-muted">{{ form.email_notifications.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check-container">
                                {{ form.weekly_reminders }}
                                <label for="{{ form.weekly_reminders.id_for_label }}" class="form-check-label">
                                    {{ form.weekly_reminders.label }}
                                </label>
                                {% if form.weekly_reminders.help_text %}
                                    <small class="form-text text-muted">{{ form.weekly_reminders.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Stats Section -->
                {% if profile.birth_date %}
                    <div class="settings-section">
                        <div class="section-header">
                            <h5 class="section-title">
                                <i class="fas fa-chart-line text-success"></i>
                                Current Statistics
                            </h5>
                            <p class="section-description">Your current life timeline statistics</p>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-birthday-cake"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value">{{ profile.age }}</div>
                                    <div class="stat-label">Years Old</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value">{{ profile.life_progress_percentage|floatformat:1 }}%</div>
                                    <div class="stat-label">Life Progress</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value">{{ profile.weeks_lived|floatformat:0 }}</div>
                                    <div class="stat-label">Weeks Lived</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-hourglass-half"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value">{{ profile.weeks_remaining|floatformat:0 }}</div>
                                    <div class="stat-label">Weeks Remaining</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-sun"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value">{{ profile.days_lived|floatformat:0 }}</div>
                                    <div class="stat-label">Days Lived</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-forward"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value">{{ profile.years_remaining }}</div>
                                    <div class="stat-label">Years Remaining</div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

        <!-- Life Timeline Preview -->
        {% if profile.birth_date %}
            <div class="timeline-preview-card mt-4">
                <div class="timeline-preview-header">
                    <h5 class="timeline-preview-title">
                        <i class="fas fa-chart-line"></i> Life Timeline Preview
                    </h5>
                    <p class="timeline-preview-subtitle">Your current life progress visualization</p>
                </div>
                <div class="timeline-preview-body">
                    <div class="life-progress-bar">
                        <div class="progress-container">
                            <div class="progress-fill" style="width: {{ profile.life_progress_percentage }}%;">
                                <div class="sand-effect"></div>
                            </div>
                            <div class="progress-text">
                                {{ profile.life_progress_percentage|floatformat:1 }}% Complete
                            </div>
                        </div>
                    </div>

                    <div class="timeline-markers mt-3">
                        <div class="marker" style="left: 0%;">Birth</div>
                        <div class="marker" style="left: 25%;">{{ profile.life_expectancy|floatformat:0|mul:0.25|floatformat:0 }} years</div>
                        <div class="marker" style="left: 50%;">{{ profile.life_expectancy|floatformat:0|mul:0.5|floatformat:0 }} years</div>
                        <div class="marker" style="left: 75%;">{{ profile.life_expectancy|floatformat:0|mul:0.75|floatformat:0 }} years</div>
                        <div class="marker" style="left: 100%;">{{ profile.life_expectancy }} years</div>
                    </div>

                    <div class="timeline-actions mt-4">
                        <a href="{% url 'weeks_view' %}" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-alt"></i> View Detailed Weeks Timeline
                        </a>
                        <a href="{% url 'dashboard' %}" class="btn btn-outline-success">
                            <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.settings-form');
    const birthDateInput = document.querySelector('#id_birth_date');
    const lifeExpectancyInput = document.querySelector('#id_life_expectancy');

    // Real-time birth date validation
    if (birthDateInput) {
        birthDateInput.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            const today = new Date();

            if (selectedDate > today) {
                this.setCustomValidity('Birth date cannot be in the future');
                this.classList.add('is-invalid');
            } else if (selectedDate < new Date('1900-01-01')) {
                this.setCustomValidity('Please enter a valid birth date');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }

    // Life expectancy validation
    if (lifeExpectancyInput) {
        lifeExpectancyInput.addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value < 50 || value > 120) {
                this.setCustomValidity('Life expectancy should be between 50 and 120 years');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }

    // Smooth scroll to sections
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Auto-save indication
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add visual feedback for unsaved changes
            const saveButton = document.querySelector('button[type="submit"]');
            if (saveButton) {
                saveButton.classList.add('btn-warning');
                saveButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Save Changes';
            }
        });
    });
});
</script>
{% endblock %}
