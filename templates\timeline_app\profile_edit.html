{% extends 'base.html' %}

{% block title %}Edit Profile - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-user-edit"></i> Edit Your Profile
                </h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" value="{{ user.username }}" readonly>
                                <small class="form-text text-muted">Username cannot be changed</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" value="{{ user.email|default:'Not set' }}" readonly>
                                <small class="form-text text-muted">Contact admin to change email</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.birth_date.id_for_label }}" class="form-label">
                            {{ form.birth_date.label }}
                        </label>
                        {{ form.birth_date }}
                        {% if form.birth_date.errors %}
                            <div class="text-danger">
                                {% for error in form.birth_date.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if form.birth_date.help_text %}
                            <small class="form-text text-muted">{{ form.birth_date.help_text }}</small>
                        {% endif %}
                    </div>
                    
                    {% if profile.birth_date %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Current Profile Information</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Current Age:</strong><br>
                                    {{ profile.age }} years old
                                </div>
                                <div class="col-md-3">
                                    <strong>Life Progress:</strong><br>
                                    {{ profile.life_progress_percentage|floatformat:1 }}%
                                </div>
                                <div class="col-md-3">
                                    <strong>Weeks Lived:</strong><br>
                                    {{ profile.weeks_lived }} weeks
                                </div>
                                <div class="col-md-3">
                                    <strong>Weeks Remaining:</strong><br>
                                    {{ profile.weeks_remaining }} weeks
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        {% if profile.birth_date %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> Life Timeline Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="life-progress-bar">
                        <div class="progress-container">
                            <div class="progress-fill" style="width: {{ profile.life_progress_percentage }}%;">
                                <div class="sand-effect"></div>
                            </div>
                            <div class="progress-text">
                                {{ profile.life_progress_percentage|floatformat:1 }}% Complete
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-markers mt-2">
                        <div class="marker" style="left: 0%;">Birth</div>
                        <div class="marker" style="left: 25%;">25 years</div>
                        <div class="marker" style="left: 50%;">50 years</div>
                        <div class="marker" style="left: 75%;">75 years</div>
                        <div class="marker" style="left: 100%;">100 years</div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="{% url 'weeks_view' %}" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-alt"></i> View Detailed Weeks Timeline
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
