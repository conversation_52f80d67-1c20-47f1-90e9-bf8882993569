{% extends 'base.html' %}

{% block title %}Apply Template - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-magic"></i> Apply Milestone Template
                    </h4>
                    <small>Project: {{ project.title }}</small>
                </div>
                
                <div class="card-body">
                    <!-- Template Preview -->
                    <div class="template-preview mb-4">
                        <h5>{{ template.name }}</h5>
                        <p class="text-muted">{{ template.description }}</p>
                        
                        <div class="template-milestones">
                            <h6>This template will create {{ template.items.count }} milestones:</h6>
                            <div class="milestone-preview-list">
                                {% for item in template.items.all %}
                                    <div class="milestone-preview-item">
                                        <div class="milestone-order">{{ item.order }}</div>
                                        <div class="milestone-info">
                                            <strong>{{ item.title }}</strong>
                                            {% if item.description %}
                                                <br><small class="text-muted">{{ item.description }}</small>
                                            {% endif %}
                                            {% if item.days_offset %}
                                                <br><small class="text-info">
                                                    <i class="fas fa-calendar"></i> 
                                                    {% if project.start_date %}
                                                        Target: {{ project.start_date|add_days:item.days_offset|date:"M d, Y" }}
                                                    {% else %}
                                                        {{ item.days_offset }} days from project start
                                                    {% endif %}
                                                </small>
                                            {% endif %}
                                            {% if item.is_critical %}
                                                <span class="badge bg-danger ms-2">Critical</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Warning if existing milestones -->
                    {% if existing_milestones > 0 %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> This project already has {{ existing_milestones }} milestone{{ existing_milestones|pluralize }}.
                            You can choose to keep them or replace them with the template.
                        </div>
                    {% endif %}
                    
                    <!-- Application Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if existing_milestones > 0 %}
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="clear_existing" name="clear_existing" value="true">
                                    <label class="form-check-label" for="clear_existing">
                                        <strong>Replace existing milestones</strong>
                                        <br><small class="text-muted">This will delete all current milestones and replace them with the template</small>
                                    </label>
                                </div>
                            </div>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'project_detail' project.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-magic"></i> Apply Template
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.template-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.milestone-preview-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    background: white;
}

.milestone-preview-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.milestone-preview-item:last-child {
    border-bottom: none;
}

.milestone-order {
    width: 30px;
    height: 30px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.milestone-info {
    flex-grow: 1;
    line-height: 1.4;
}

/* Dark theme support */
[data-theme="dark"] .template-preview {
    background: #343a40;
    border-color: #495057;
}

[data-theme="dark"] .milestone-preview-list {
    background: #212529;
    border-color: #495057;
}

[data-theme="dark"] .milestone-preview-item {
    border-bottom-color: #495057;
}
</style>
{% endblock %}
