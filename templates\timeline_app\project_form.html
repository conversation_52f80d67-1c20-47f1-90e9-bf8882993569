{% extends 'base.html' %}

{% block title %}{{ title }} - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-{% if project %}edit{% else %}plus{% endif %}"></i> {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">
                            {{ form.title.label }} <span class="text-danger">*</span>
                        </label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger">
                                {% for error in form.title.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            {{ form.description.label }}
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.deadline.id_for_label }}" class="form-label">
                                    {{ form.deadline.label }}
                                </label>
                                {{ form.deadline }}
                                {% if form.deadline.errors %}
                                    <div class="text-danger">
                                        {% for error in form.deadline.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.deadline.help_text %}
                                    <small class="form-text text-muted">{{ form.deadline.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.priority.id_for_label }}" class="form-label">
                                    {{ form.priority.label }}
                                </label>
                                {{ form.priority }}
                                {% if form.priority.errors %}
                                    <div class="text-danger">
                                        {% for error in form.priority.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">
                            {{ form.status.label }}
                        </label>
                        {{ form.status }}
                        {% if form.status.errors %}
                            <div class="text-danger">
                                {% for error in form.status.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Project Tips</h6>
                        <ul class="mb-0">
                            <li><strong>Title:</strong> Keep it clear and specific</li>
                            <li><strong>Deadline:</strong> Leave empty for lifetime projects that extend to your 100th birthday</li>
                            <li><strong>Priority:</strong> Use this to organize your projects by importance</li>
                            <li><strong>Status:</strong> Update this as you make progress</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> 
                            {% if project %}Update Project{% else %}Create Project{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        {% if project %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> Project Progress
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>Current Progress</h6>
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar bg-success" style="width: {{ project.progress_percentage }}%"></div>
                                </div>
                                <span class="badge bg-primary">{{ project.progress_percentage|floatformat:0 }}%</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>Status</h6>
                                <span class="badge bg-{% if project.status == 'completed' %}success{% elif project.status == 'in_progress' %}primary{% elif project.status == 'on_hold' %}warning{% elif project.status == 'cancelled' %}danger{% else %}secondary{% endif %} fs-6">
                                    {{ project.get_status_display }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>
                                    {% if project.deadline %}
                                        Days Remaining
                                    {% else %}
                                        Project Type
                                    {% endif %}
                                </h6>
                                {% if project.deadline %}
                                    <span class="badge bg-{% if project.is_overdue %}danger{% elif project.days_remaining < 7 %}warning{% else %}info{% endif %} fs-6">
                                        {% if project.is_overdue %}
                                            Overdue
                                        {% else %}
                                            {{ project.days_remaining }} days
                                        {% endif %}
                                    </span>
                                {% else %}
                                    <span class="badge bg-info fs-6">Lifetime Project</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if project.deadline %}
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> 
                                Created: {{ project.created_at|date:"M d, Y" }} | 
                                Deadline: {{ project.deadline|date:"M d, Y" }}
                            </small>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
