#!/usr/bin/env python
import os
import django
from django.conf import settings
from django.contrib.auth.models import User

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'timeline_project.settings')
django.setup()

# Create superuser
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created successfully!')
    print('Username: admin')
    print('Password: admin123')
else:
    print('Superuser already exists!')
