#!/usr/bin/env python
import os
import django
from django.conf import settings
from django.contrib.auth.models import User
from datetime import date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'timeline_project.settings')
django.setup()

from timeline_app.models import UserProfile

# Create test user
username = 'testuser'
password = 'testpass123'
email = '<EMAIL>'

if not User.objects.filter(username=username).exists():
    user = User.objects.create_user(username=username, password=password, email=email)
    user.first_name = 'John'
    user.last_name = 'Doe'
    user.save()
    
    # Create profile with sample data
    profile = UserProfile.objects.create(
        user=user,
        birth_date=date(1990, 5, 15),  # 34 years old
        timezone='US/Eastern',
        theme='light',
        life_expectancy=85,
        bio='I am a software developer passionate about productivity and life optimization.',
        location='New York, USA',
        website='https://johndoe.com',
        email_notifications=True,
        weekly_reminders=True
    )
    
    print(f'Test user created successfully!')
    print(f'Username: {username}')
    print(f'Password: {password}')
    print(f'Age: {profile.age} years')
    print(f'Life Progress: {profile.life_progress_percentage:.1f}%')
else:
    print('Test user already exists!')
    print(f'Username: {username}')
    print(f'Password: {password}')

# Also create superuser if it doesn't exist
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin / admin123')
else:
    print('Superuser already exists: admin / admin123')
