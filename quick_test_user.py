import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'timeline_project.settings')
django.setup()

from django.contrib.auth.models import User
from timeline_app.models import UserProfile
from datetime import date

# Create test user
try:
    user = User.objects.create_user('demo', '<EMAIL>', 'demo123')
    user.first_name = 'Demo'
    user.last_name = 'User'
    user.save()
    
    profile = UserProfile.objects.create(
        user=user,
        birth_date=date(1990, 1, 1),
        life_expectancy=80,
        bio='Demo user for testing',
        location='Test City'
    )
    print('Demo user created: demo / demo123')
except:
    print('Demo user already exists: demo / demo123')
