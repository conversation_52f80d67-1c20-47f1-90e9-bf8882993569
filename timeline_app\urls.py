from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    # Landing and authentication
    path('', views.landing_page, name='landing'),
    path('register/', views.register_view, name='register'),
    path('login/', auth_views.LoginView.as_view(), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    
    # Main dashboard
    path('dashboard/', views.dashboard, name='dashboard'),
    
    # Profile management
    path('profile/edit/', views.profile_edit, name='profile_edit'),
    
    # Timeline views
    path('weeks/', views.weeks_view, name='weeks_view'),
    
    # Project management
    path('project/create/', views.project_create, name='project_create'),
    path('project/<int:project_id>/edit/', views.project_edit, name='project_edit'),
    path('project/<int:project_id>/delete/', views.project_delete, name='project_delete'),
    
    # API endpoints
    path('api/current-time/', views.get_current_time, name='current_time'),
]
