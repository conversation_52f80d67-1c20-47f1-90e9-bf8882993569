from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    # Landing and authentication
    path('', views.landing_page, name='landing'),
    path('register/', views.register_view, name='register'),
    path('login/', auth_views.LoginView.as_view(), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    
    # Main dashboard
    path('dashboard/', views.dashboard, name='dashboard'),
    
    # Profile management
    path('profile/edit/', views.profile_edit, name='profile_edit'),
    
    # Timeline views
    path('weeks/', views.weeks_view, name='weeks_view'),
    path('completed/', views.completed_projects, name='completed_projects'),
    path('life-timeline/', views.life_timeline_view, name='life_timeline'),

    # Project management
    path('project/create/', views.project_create, name='project_create'),
    path('projects/<int:project_id>/', views.project_detail, name='project_detail'),
    path('project/<int:project_id>/edit/', views.project_edit, name='project_edit'),
    path('project/<int:project_id>/delete/', views.project_delete, name='project_delete'),

    # Dream Wall / Vision Board
    path('dreams/', views.dream_wall, name='dream_wall'),
    path('dreams/add/', views.dream_item_create, name='dream_create'),
    path('dreams/<int:dream_id>/', views.dream_detail, name='dream_detail'),
    path('dreams/<int:dream_id>/edit/', views.dream_item_edit, name='dream_edit'),
    path('dreams/<int:dream_id>/delete/', views.dream_item_delete, name='dream_delete'),
    path('dreams/<int:dream_id>/achieve/', views.dream_item_achieve, name='dream_achieve'),
    path('dreams/randomize/', views.randomize_dream_positions, name='randomize_dreams'),

    # API endpoints
    path('api/current-time/', views.get_current_time, name='current_time'),

    # Milestone management
    path('projects/<int:project_id>/milestones/add/', views.milestone_create, name='milestone_create'),
    path('projects/<int:project_id>/templates/<int:template_id>/apply/', views.apply_milestone_template, name='apply_milestone_template'),
    path('projects/<int:project_id>/milestones/bulk/', views.bulk_milestone_action, name='bulk_milestone_action'),
    path('projects/<int:project_id>/milestones/export/', views.export_project_milestones, name='export_project_milestones'),
    path('milestones/<int:milestone_id>/edit/', views.milestone_edit, name='milestone_edit'),
    path('milestones/<int:milestone_id>/toggle/', views.milestone_toggle, name='milestone_toggle'),
    path('milestones/<int:milestone_id>/delete/', views.milestone_delete, name='milestone_delete'),

    # Dream milestone management
    path('dreams/<int:dream_id>/milestones/add/', views.dream_milestone_create, name='dream_milestone_create'),
    path('dream-milestones/<int:milestone_id>/toggle/', views.dream_milestone_toggle, name='dream_milestone_toggle'),
    path('dream-milestones/<int:milestone_id>/delete/', views.dream_milestone_delete, name='dream_milestone_delete'),
]
