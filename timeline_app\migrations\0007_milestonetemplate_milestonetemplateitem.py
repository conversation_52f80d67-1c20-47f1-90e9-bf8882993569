# Generated by Django 4.2.7 on 2025-07-10 18:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('timeline_app', '0006_milestone_dreammilestone'),
    ]

    operations = [
        migrations.CreateModel(
            name='MilestoneTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('web_development', 'Web Development'), ('mobile_app', 'Mobile App'), ('learning', 'Learning/Education'), ('fitness', 'Fitness/Health'), ('business', 'Business/Startup'), ('creative', 'Creative Project'), ('travel', 'Travel Planning'), ('career', 'Career Development'), ('financial', 'Financial Goal'), ('general', 'General Project')], max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='MilestoneTemplateItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('order', models.PositiveIntegerField()),
                ('days_offset', models.PositiveIntegerField(help_text='Days from project start')),
                ('is_critical', models.BooleanField(default=False, help_text='Critical milestone that affects project success')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='timeline_app.milestonetemplate')),
            ],
            options={
                'ordering': ['order'],
                'unique_together': {('template', 'order')},
            },
        ),
    ]
