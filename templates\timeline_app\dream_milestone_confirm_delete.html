{% extends 'base.html' %}

{% block title %}Delete Dream Milestone - TimeLine{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Delete Dream Milestone
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="milestone-preview mb-4">
                        <h5>{{ milestone.title }}</h5>
                        {% if milestone.description %}
                            <p class="text-muted">{{ milestone.description }}</p>
                        {% endif %}
                        <small class="text-muted">
                            Dream: {{ dream.title }}
                            {% if milestone.target_date %}
                                | Target: {{ milestone.target_date|date:"M d, Y" }}
                            {% endif %}
                        </small>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning:</strong> This action cannot be undone. 
                        Deleting this milestone will permanently remove it from your dream.
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'dream_detail' dream.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete Milestone
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
