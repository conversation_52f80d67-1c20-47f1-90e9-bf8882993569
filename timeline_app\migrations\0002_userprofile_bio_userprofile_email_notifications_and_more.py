# Generated by Django 5.2.4 on 2025-07-08 19:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('timeline_app', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='bio',
            field=models.TextField(blank=True, help_text='Tell us about yourself', max_length=500),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='email_notifications',
            field=models.BooleanField(default=True, help_text='Receive email notifications'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='life_expectancy',
            field=models.IntegerField(default=100, help_text='Expected lifespan in years'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='location',
            field=models.CharField(blank=True, help_text='Your current location', max_length=100),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='theme',
            field=models.CharField(choices=[('light', 'Light Theme'), ('dark', 'Dark Theme'), ('auto', 'Auto (System)')], default='light', max_length=10),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='timezone',
            field=models.CharField(choices=[('UTC', 'UTC'), ('US/Eastern', 'Eastern Time'), ('US/Central', 'Central Time'), ('US/Mountain', 'Mountain Time'), ('US/Pacific', 'Pacific Time'), ('Europe/London', 'London'), ('Europe/Paris', 'Paris'), ('Europe/Berlin', 'Berlin'), ('Asia/Tokyo', 'Tokyo'), ('Asia/Shanghai', 'Shanghai'), ('Australia/Sydney', 'Sydney')], default='UTC', max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='website',
            field=models.URLField(blank=True, help_text='Your personal website or social media'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='weekly_reminders',
            field=models.BooleanField(default=True, help_text='Weekly progress reminders'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='birth_date',
            field=models.DateField(blank=True, help_text='Used to calculate your life timeline', null=True),
        ),
    ]
