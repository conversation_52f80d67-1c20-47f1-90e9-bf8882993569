"""
Smart Notification Service
Generates intelligent notifications based on user behavior and patterns
"""

from django.utils import timezone
from datetime import timedelta, date
from .models import Notification, Project, Milestone, DreamItem, DreamMilestone
from django.contrib.auth.models import User


class NotificationService:
    """Service for generating and managing smart notifications"""
    
    @staticmethod
    def generate_daily_notifications(user):
        """Generate daily notifications for a user"""
        notifications = []
        
        # Check for overdue milestones
        notifications.extend(NotificationService._check_overdue_milestones(user))
        
        # Check for stalled projects
        notifications.extend(NotificationService._check_stalled_projects(user))
        
        # Check for streak opportunities
        notifications.extend(NotificationService._check_streak_opportunities(user))
        
        # Check for achievements
        notifications.extend(NotificationService._check_achievements(user))
        
        # Generate smart suggestions
        notifications.extend(NotificationService._generate_smart_suggestions(user))
        
        return notifications
    
    @staticmethod
    def _check_overdue_milestones(user):
        """Check for overdue milestones"""
        notifications = []
        now = timezone.now()
        
        # Project milestones
        overdue_milestones = Milestone.objects.filter(
            project__user=user,
            target_date__lt=now.date(),
            is_completed=False
        )
        
        for milestone in overdue_milestones:
            days_overdue = (now.date() - milestone.target_date).days
            
            # Don't spam - only notify once per week for each overdue milestone
            existing_notification = Notification.objects.filter(
                user=user,
                type='milestone_overdue',
                related_milestone=milestone,
                created_at__gte=now - timedelta(days=7)
            ).exists()
            
            if not existing_notification:
                priority = 'urgent' if days_overdue > 7 else 'high'
                notifications.append(Notification(
                    user=user,
                    type='milestone_overdue',
                    priority=priority,
                    title=f'Milestone Overdue: {milestone.title}',
                    message=f'This milestone is {days_overdue} day{"s" if days_overdue != 1 else ""} overdue. Consider updating the target date or completing it soon.',
                    action_url=f'/projects/{milestone.project.id}/',
                    action_text='View Project',
                    related_project=milestone.project,
                    related_milestone=milestone,
                    expires_at=now + timedelta(days=7)
                ))
        
        # Dream milestones
        overdue_dream_milestones = DreamMilestone.objects.filter(
            dream__user=user,
            target_date__lt=now.date(),
            is_completed=False
        )
        
        for milestone in overdue_dream_milestones:
            days_overdue = (now.date() - milestone.target_date).days
            
            existing_notification = Notification.objects.filter(
                user=user,
                type='milestone_overdue',
                related_dream=milestone.dream,
                created_at__gte=now - timedelta(days=7)
            ).exists()
            
            if not existing_notification:
                priority = 'urgent' if days_overdue > 7 else 'high'
                notifications.append(Notification(
                    user=user,
                    type='milestone_overdue',
                    priority=priority,
                    title=f'Dream Milestone Overdue: {milestone.title}',
                    message=f'This dream milestone is {days_overdue} day{"s" if days_overdue != 1 else ""} overdue. Keep chasing your dreams!',
                    action_url=f'/dreams/{milestone.dream.id}/',
                    action_text='View Dream',
                    related_dream=milestone.dream,
                    expires_at=now + timedelta(days=7)
                ))
        
        return notifications
    
    @staticmethod
    def _check_stalled_projects(user):
        """Check for projects that haven't been updated recently"""
        notifications = []
        now = timezone.now()
        two_weeks_ago = now - timedelta(days=14)
        
        stalled_projects = Project.objects.filter(
            user=user,
            status__in=['not_started', 'in_progress'],
            updated_at__lt=two_weeks_ago
        )
        
        for project in stalled_projects:
            days_stalled = (now - project.updated_at).days
            
            # Don't spam - only notify once per month for stalled projects
            existing_notification = Notification.objects.filter(
                user=user,
                type='project_stalled',
                related_project=project,
                created_at__gte=now - timedelta(days=30)
            ).exists()
            
            if not existing_notification:
                notifications.append(Notification(
                    user=user,
                    type='project_stalled',
                    priority='medium',
                    title=f'Project Needs Attention: {project.title}',
                    message=f'This project hasn\'t been updated in {days_stalled} days. Consider adding a milestone or updating its status.',
                    action_url=f'/projects/{project.id}/',
                    action_text='Review Project',
                    related_project=project,
                    expires_at=now + timedelta(days=30)
                ))
        
        return notifications
    
    @staticmethod
    def _check_streak_opportunities(user):
        """Check for streak building opportunities"""
        notifications = []
        now = timezone.now()
        yesterday = now - timedelta(days=1)
        
        # Check if user completed any milestones yesterday
        completed_yesterday = (
            Milestone.objects.filter(
                project__user=user,
                completed_date__date=yesterday.date(),
                is_completed=True
            ).exists() or
            DreamMilestone.objects.filter(
                dream__user=user,
                completed_date__date=yesterday.date(),
                is_completed=True
            ).exists()
        )
        
        # Check if user completed any milestones today
        completed_today = (
            Milestone.objects.filter(
                project__user=user,
                completed_date__date=now.date(),
                is_completed=True
            ).exists() or
            DreamMilestone.objects.filter(
                dream__user=user,
                completed_date__date=now.date(),
                is_completed=True
            ).exists()
        )
        
        # If completed yesterday but not today, encourage to continue streak
        if completed_yesterday and not completed_today:
            existing_notification = Notification.objects.filter(
                user=user,
                type='suggestion',
                title__icontains='streak',
                created_at__gte=now.replace(hour=0, minute=0, second=0)
            ).exists()
            
            if not existing_notification:
                notifications.append(Notification(
                    user=user,
                    type='suggestion',
                    priority='medium',
                    title='Keep Your Streak Alive! 🔥',
                    message='You completed milestones yesterday! Complete one today to maintain your momentum.',
                    action_url='/dashboard/',
                    action_text='View Dashboard',
                    expires_at=now.replace(hour=23, minute=59, second=59)
                ))
        
        return notifications
    
    @staticmethod
    def _check_achievements(user):
        """Check for new achievements to celebrate"""
        notifications = []
        now = timezone.now()
        
        try:
            profile = user.userprofile
            
            # Check for milestone completion milestones (10, 25, 50, 100, etc.)
            total_completed = (
                Milestone.objects.filter(project__user=user, is_completed=True).count() +
                DreamMilestone.objects.filter(dream__user=user, is_completed=True).count()
            )
            
            achievement_thresholds = [10, 25, 50, 100, 250, 500, 1000]
            
            for threshold in achievement_thresholds:
                if total_completed >= threshold:
                    # Check if we already celebrated this achievement
                    existing_celebration = Notification.objects.filter(
                        user=user,
                        type='achievement_unlocked',
                        title__icontains=f'{threshold} milestones'
                    ).exists()
                    
                    if not existing_celebration:
                        notifications.append(Notification(
                            user=user,
                            type='achievement_unlocked',
                            priority='high',
                            title=f'🏆 Achievement Unlocked: {threshold} Milestones!',
                            message=f'Congratulations! You\'ve completed {total_completed} milestones. You\'re unstoppable!',
                            action_url='/analytics/',
                            action_text='View Analytics',
                            expires_at=now + timedelta(days=30)
                        ))
                        break  # Only celebrate the highest achieved threshold
            
        except:
            pass  # Profile doesn't exist
        
        return notifications
    
    @staticmethod
    def _generate_smart_suggestions(user):
        """Generate smart suggestions based on user patterns"""
        notifications = []
        now = timezone.now()
        
        # Suggest adding milestones to projects without them
        projects_without_milestones = Project.objects.filter(
            user=user,
            status__in=['not_started', 'in_progress'],
            milestones__isnull=True
        )
        
        if projects_without_milestones.exists():
            # Don't spam - only suggest once per week
            existing_suggestion = Notification.objects.filter(
                user=user,
                type='suggestion',
                title__icontains='milestones',
                created_at__gte=now - timedelta(days=7)
            ).exists()
            
            if not existing_suggestion:
                project = projects_without_milestones.first()
                notifications.append(Notification(
                    user=user,
                    type='suggestion',
                    priority='low',
                    title='💡 Boost Your Success Rate',
                    message=f'Add milestones to "{project.title}" to increase completion rate by 60%!',
                    action_url=f'/projects/{project.id}/milestones/add/',
                    action_text='Add Milestones',
                    related_project=project,
                    expires_at=now + timedelta(days=7)
                ))
        
        return notifications
    
    @staticmethod
    def create_celebration_notification(user, milestone, project=None, dream=None):
        """Create a celebration notification for milestone completion"""
        now = timezone.now()
        
        if project:
            progress = project.get_progress_percentage()
            if progress >= 100:
                title = f'🎉 Project Complete: {project.title}!'
                message = 'Congratulations! You\'ve completed all milestones for this project!'
            elif progress >= 80:
                title = f'🚀 Almost There: {milestone.title}'
                message = f'Amazing! You\'re {progress:.0f}% done with {project.title}!'
            else:
                title = f'✅ Milestone Complete: {milestone.title}'
                message = f'Great job! You\'re making excellent progress on {project.title}.'
            
            action_url = f'/projects/{project.id}/'
        else:
            title = f'✨ Dream Milestone Complete: {milestone.title}'
            message = f'You\'re one step closer to achieving your dream: {dream.title}!'
            action_url = f'/dreams/{dream.id}/'
        
        return Notification.objects.create(
            user=user,
            type='celebration',
            priority='medium',
            title=title,
            message=message,
            action_url=action_url,
            action_text='View Details',
            related_project=project,
            related_dream=dream,
            expires_at=now + timedelta(days=3)
        )
