{% extends 'base.html' %}

{% block title %}Achieve Dream - TimeLine{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0">
                    <i class="fas fa-trophy text-warning"></i> Mark as Achieved
                </h4>
            </div>
            <div class="card-body text-center">
                <div class="achievement-celebration mb-4">
                    <i class="fas fa-star fa-3x text-warning mb-3"></i>
                    <h5>Congratulations!</h5>
                    <p class="lead">You're about to mark this dream as achieved:</p>
                </div>
                
                <div class="dream-preview mb-4">
                    {% if dream.image_display_url %}
                    <div class="dream-image mb-3">
                        <img src="{{ dream.image_display_url }}" alt="{{ dream.title }}"
                             style="max-width: 200px; max-height: 150px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                    </div>
                    {% endif %}
                    
                    <h4 class="text-primary">{{ dream.title }}</h4>
                    {% if dream.description %}
                    <p class="text-muted">{{ dream.description }}</p>
                    {% endif %}
                    
                    <div class="dream-meta">
                        <span class="badge bg-{{ dream.category }} me-2">{{ dream.get_category_display }}</span>
                        <span class="badge bg-info">Priority: {{ dream.priority }}/10</span>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="form-actions">
                        <button type="submit" class="btn btn-success btn-lg me-3">
                            <i class="fas fa-trophy"></i> Yes, I Achieved This!
                        </button>
                        <a href="{% url 'dream_wall' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add celebration animation
    const celebration = document.querySelector('.achievement-celebration');
    celebration.style.opacity = '0';
    celebration.style.transform = 'scale(0.8)';
    
    setTimeout(() => {
        celebration.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
        celebration.style.opacity = '1';
        celebration.style.transform = 'scale(1)';
    }, 200);
    
    // Animate dream preview
    const preview = document.querySelector('.dream-preview');
    preview.style.opacity = '0';
    preview.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        preview.style.transition = 'all 0.6s ease';
        preview.style.opacity = '1';
        preview.style.transform = 'translateY(0)';
    }, 400);
});
</script>
{% endblock %}
